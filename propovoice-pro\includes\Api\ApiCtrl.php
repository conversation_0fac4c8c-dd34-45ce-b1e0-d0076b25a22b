<?php

namespace Ndpvp\Api;

use Ndpvp\Api\Type\Action;
use Ndpvp\Api\Type\CustomField;
use Ndpvp\Api\Type\Form;
use Ndpvp\Api\Type\License;
use Ndpvp\Api\Type\Message;
use Ndpvp\Api\Type\Setting;
use Ndpvp\Api\Type\PaymentProcess;
use Ndpvp\Api\Type\Staff;
use Ndpvp\Api\Type\WooCommerce\WCOrder;
use Ndpvp\Api\Type\Subscription;
use Ndpvp\Api\Type\PublicApi;

class ApiCtrl {


	public function __construct() {
		new Form();
		new Setting();
		new License();
		new PaymentProcess();
		new WCOrder();
		new Subscription();
		new CustomField();
		new Staff();
		new Action();
		new Message();
		new PublicApi();
	}
}

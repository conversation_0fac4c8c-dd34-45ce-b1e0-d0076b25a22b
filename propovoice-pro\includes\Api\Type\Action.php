<?php
namespace Ndpvp\Api\Type;

use Ndpv\Helpers\Fns;

class Action {

    public function __construct() {
        add_action( 'rest_api_init', [ $this, 'rest_routes' ] );
    }

    public function rest_routes() {
        register_rest_route(
            'ndpvp/v1', '/actions', [
				'methods' => 'POST',
				'callback' => [ $this, 'create' ],
				'permission_callback' => [ $this, 'create_per' ],
			]
        );
    }

    public function create( $req ) {
        $param = $req->get_params();
        $reg_errors = new \WP_Error();

        $id = isset( $param['id'] ) ? absint( $param['id'] ) : null;
        $from = isset( $param['from'] )
            ? sanitize_text_field( $param['from'] )
            : null;
        $type = isset( $param['type'] )
            ? sanitize_text_field( $param['type'] )
            : null;

        if ( empty( $id ) || empty( $type ) ) {
            $reg_errors->add(
                'field',
                esc_html__( 'Required field is missing', 'propovoice-pro' )
            );
        }

        if ( $reg_errors->get_error_messages() ) {
            wp_send_json_error( $reg_errors->get_error_messages() );
        } else {
            if ( $type === 'resend-password' ) {
                if ( $from === 'client_portal' ) {
                    $args = [
                        'meta_key' => 'ndpv_client_id',
                        'meta_value' => $id,
                        'meta_compare' => '=',
                        'number' => 1,
                    ];

                    $user_query = new \WP_User_Query( $args );
                    $users = $user_query->get_results();
                    if ( ! empty( $users ) ) {
                        $id = $users[0]->data->ID;
                    }
                }

                $password = wp_generate_password( $length = 12, $is_include_standard_special_chars = false );
                wp_set_password( $password, $id );

                $user = get_user_by( 'id', $id );
                $name = $user->data->display_name;
                $email = $user->data->user_email;

                $sent_mail = Fns::password_mail( $name, $email, $password, $from );
                if ( ! $sent_mail ) {
                    wp_send_json_error( [ 'Password not sent' ] );
                }
            }
            wp_send_json_success();
        }
    }

    public function create_per() {
        return current_user_can( 'ndpv_action' );
    }
}

<?php
namespace Ndpvp\Api\Type;

use Ndpvp\Models\CustomField as ModelCustomField;
use Ndpv\Helpers\Fns;

class CustomField {


    public function __construct() {
        add_action( 'rest_api_init', [ $this, 'rest_routes' ] );
    }

    public function rest_routes() {

        register_rest_route(
            'ndpv/v1', '/custom-fields/(?P<id>\d+)', [
				'methods' => 'GET',
				'callback' => [ $this, 'get_single' ],
				'permission_callback' => [ $this, 'get_per' ],
				'args' => [
					'id' => [
						'validate_callback' => function ( $param, $request, $key ) {
							return is_numeric( $param );
						},
					],
				],
			]
        );

        register_rest_route(
            'ndpv/v1', '/custom-fields' . ndpv()->plain_route(), [
				'methods' => 'GET',
				'callback' => [ $this, 'get' ],
				'permission_callback' => [ $this, 'get_per' ],
			]
        );

        register_rest_route(
            'ndpv/v1', '/custom-fields', [
				'methods' => 'POST',
				'callback' => [ $this, 'create' ],
				'permission_callback' => [ $this, 'create_per' ],
			]
        );

        register_rest_route(
            'ndpv/v1', '/custom-fields/(?P<id>\d+)', [
				'methods' => 'PUT',
				'callback' => [ $this, 'update' ],
				'permission_callback' => [ $this, 'update_per' ],
				'args' => [
					'id' => [
						'validate_callback' => function ( $param, $request, $key ) {
							return is_numeric( $param );
						},
					],
				],
			]
        );

        register_rest_route(
            'ndpv/v1', '/custom-fields/(?P<id>[0-9,]+)', [
				'methods' => 'DELETE',
				'callback' => [ $this, 'delete' ],
				'permission_callback' => [ $this, 'del_per' ],
				'args' => [
					'id' => [
						'sanitize_callback'  => 'sanitize_text_field',
					],
				],
			]
        );
    }

    public function get( $req ) {
        $param = $req->get_params();
        $reg_errors = new \WP_Error();

        $mod = isset( $param['mod'] ) ? sanitize_text_field( $param['mod'] ) : null;

        if ( empty( $mod ) ) {
            $reg_errors->add( 'field', esc_html__( 'Module is missing', 'propovoice-pro' ) );
        }

        if ( $reg_errors->get_error_messages() ) {
            wp_send_json_error( $reg_errors->get_error_messages() );
        } else {
            $custom_field = new ModelCustomField();
            $data = $custom_field->get( $mod );

            wp_send_json_success( $data );
        }
    }

    public function get_single( $req ) {
        $url_params = $req->get_url_params();
        $id = $url_params['id'];
        $query_data = [];
        $query_data['id'] = $id;

        wp_send_json_success( $query_data );
    }

    public function create( $req ) {
        $param = $req->get_params();
        $reg_errors = new \WP_Error();
        $data = [];
        $reorder = isset( $param['reorder'] ) ? array_map( 'absint', $param['reorder'] ) : false;
        $data['mod'] = isset( $param['mod'] ) ? sanitize_text_field( $param['mod'] ) : '';
        $data['label'] = isset( $param['label'] ) ? sanitize_text_field( $param['label'] ) : '';
        $data['desc'] = isset( $param['desc'] ) ? sanitize_text_field( $param['desc'] ) : '';
        $data['type'] = isset( $param['type'] ) ? sanitize_text_field( $param['type'] ) : '';
        $data['options'] = isset( $param['options'] ) ? maybe_serialize( $param['options'] ) : '';
        if ( $data['type'] == 'multi-select' && ! $param['value'] ) {
            $param['value'] = [];
        }

        $data['value'] = isset( $param['value'] ) ? maybe_serialize( $param['value'] ) : '';

        if ( empty( $data['mod'] ) ) {
            $reg_errors->add( 'field', esc_html__( 'Module is missing', 'propovoice-pro' ) );
        }

        $slug = Fns::slugify_key( $data['label'] );

        if ( $slug === 'date' ) {
            $reg_errors->add( 'date_exist', esc_html__( 'Date field already used', 'propovoice-pro' ) );
        }

        $custom_field = new ModelCustomField();
        $custom_field_data = $custom_field->get( $data['mod'] );
        if ( $custom_field_data ) {
            $exist = false;
            foreach ( $custom_field_data as $field ) {
                if ( $field['slug'] === $slug ) {
                    $exist = true;
                    break;
                }
            }

            if ( $exist ) {
                $reg_errors->add( 'already_exist', esc_html__( 'Field alaready exist', 'propovoice-pro' ) );
            }
        }

        if ( $reg_errors->get_error_messages() ) {
            wp_send_json_error( $reg_errors->get_error_messages() );
        } elseif ( $reorder ) {
                $this->reorder_taxonomies( $reorder );
                wp_send_json_success();
		} else {
			global $wpdb;
			$tbl_name = $wpdb->prefix . 'ndpv_custom_fields';

			$data['slug'] = $slug;
			$data['time'] = current_time( 'mysql', false );

			$insert = $wpdb->insert( $tbl_name, $data );

			if ( ! is_wp_error( $insert ) ) {
				wp_send_json_success( $insert );
			} else {
				wp_send_json_error();
			}
        }
    }

    public function update( $req ) {
        $param = $req->get_params();
        $reg_errors = new \WP_Error();

        $data = [];

        $data['mod'] = isset( $param['mod'] ) ? sanitize_text_field( $param['mod'] ) : '';
        $data['label'] = isset( $param['label'] ) ? sanitize_text_field( $param['label'] ) : '';
        $data['desc'] = isset( $param['desc'] ) ? sanitize_text_field( $param['desc'] ) : '';
        $data['type'] = isset( $param['type'] ) ? sanitize_text_field( $param['type'] ) : '';
        $data['options'] = isset( $param['options'] ) ? maybe_serialize( $param['options'] ) : '';
        if ( $data['type'] == 'multi-select' && ! $param['value'] ) {
            $param['value'] = [];
        }
        $data['value'] = isset( $param['value'] ) ? maybe_serialize( $param['value'] ) : '';

        if ( empty( $data['mod'] ) ) {
            $reg_errors->add( 'field', esc_html__( 'Module is missing', 'propovoice-pro' ) );
        }

        if ( $reg_errors->get_error_messages() ) {
            wp_send_json_error( $reg_errors->get_error_messages() );
        } else {
            global $wpdb;

            $url_params = $req->get_url_params();
            $id = absint( $url_params['id'] );
            $tbl_name = $wpdb->prefix . 'ndpv_custom_fields';

            $data['slug'] = Fns::slugify_key( $data['label'] );
            $data['time'] = current_time( 'mysql', false );

            $update = $wpdb->update( $tbl_name, $data, [ 'id' => $id ] );

            if ( ! is_wp_error( $update ) ) {
                wp_send_json_success( $update );
            } else {
                wp_send_json_error();
            }
        }
    }

    public function reorder_taxonomies( $ids = [] ) {
        global $wpdb;
        $tbl_name = $wpdb->prefix . 'ndpv_custom_fields';
        $i = 1;
        foreach ( $ids as $id ) :
            $data = [];
            $data['order'] = $i;
            $wpdb->update( $tbl_name, $data, [ 'id' => $id ] );

            ++$i;
        endforeach;
    }

    public function delete( $req ) {
        $url_params = $req->get_url_params();
        global $wpdb;
        $tbl_name = $wpdb->prefix . 'ndpv_custom_fields';

        $ids = explode( ',', $url_params['id'] );
        foreach ( $ids as $id ) {
            $wpdb->delete(
                $tbl_name,
                [ 'id' => $id ], //which id need to delete
                [ '%d' ], //make sure the id format
            );
        }
        wp_send_json_success( $ids );
    }

    // check permission
    public function get_per() {
        return true;
        // return current_user_can('publish_posts');
    }

    public function create_per() {
        return current_user_can( 'publish_posts' );
    }

    public function update_per() {
        return current_user_can( 'edit_posts' );
    }

    public function del_per() {
        return current_user_can( 'delete_posts' );
    }
}

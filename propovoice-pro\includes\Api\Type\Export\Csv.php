<?php

namespace Ndpvp\Api\Type\Export;

use Ndpv\Helpers\Fns;

class Csv {

    function export( \WP_REST_Request $request ) {
        $params = $request->get_json_params();
        $title = $params['title'] ?? '';

        // $fields = [
        //     "first_name" => "First name",
        // ]

        $fields = $params['fields'] ?? [];

        $headers = array_map(
            function ( $slug, $label ) {
				if ( $slug === 'first_name' ) {
					return 'NAME';
				}
				if ( $slug === 'org_name' ) {
					return 'ORGANIZATION';
				}
				return strtoupper( $label );
			}, array_keys( $fields ), $fields
        );

        $post_type =
            $title === 'lead'
            ? [ 'ndpv_lead' ]
            : ( $title === 'client'
                ? [ 'ndpv_person', 'ndpv_org' ]
                : [] );
        $data = $this->get_data( $post_type );
        $this->download( $title, $headers, $fields, $data );
    }

    function get_data( $post_type ) {
        return get_posts(
            [
				'post_type' => $post_type,
				'posts_per_page' => -1,
				'post_status' => 'publish',
			]
        );
    }



    function download( $title, $headers, $fields, $data ) {
        $this->set_headers( ucfirst( $title ) );

        $output = fopen( 'php://output', 'w' );

        $this->build_csv( $output, $headers );
        // fputcsv($output, $headers);

        foreach ( $data as $row ) {
            $in = $this->maybe_in( $title, $row->ID );
            if ( ! $in ) {
                continue;
            }

            $post_meta = get_post_meta( $row->ID );

            // Get person and org meta
            $post_meta = $this->get_ref_meta( $post_meta );

            if ( $title == 'lead' ) {
                $post_meta = $this->maybe_get_lead_data( $row, $post_meta );
            }

            $row = $this->map_entity( $fields, $post_meta, $title );

            $this->build_csv( $output, $row );

            // fputcsv($output, $row);
        }
        fclose( $output );
        exit; // Terminate script after sending the file
        // return $output;
    }

    function build_csv( $output, $row ) {
            $csv_row = '';
		foreach ( $row as $value ) {
			$value = $this->maybe_multiselect( $value );
			$csv_row .= $value . ',';
		}
            // Remove only one trailing comma
		if ( ! empty( $csv_row ) ) {
			$csv_row = substr( $csv_row, 0, -1 );
		}
            fwrite( $output, $csv_row . "\n" ); // Write the CSV line to the file
    }

    function maybe_multiselect( $value ) {
        // Unserialize field data if serialized
        $field_data = maybe_unserialize( $value );
        if ( is_array( $field_data ) ) {
            $field_data = implode( ',', $field_data );

            // Separate multiselect value with "&"
            $value = str_replace( ',', '&', $field_data );
        }

        return $value;
    }

    // get meta for person_id and org_id
    function get_ref_meta( $post_meta ) {
        // Get meta for person_id
		if ( isset( $post_meta['person_id'][0] ) ) {
			$person_meta = get_post_meta( (int) $post_meta['person_id'][0] ) ?? [];
			$post_meta = array_merge( $post_meta, $person_meta );
		}

        // Get meta for org_id
		if ( isset( $post_meta['org_id'][0] ) ) {
			$org_meta = get_post_meta( (int) $post_meta['org_id'][0] ) ?? [];
			$post_meta = array_merge( $post_meta, $org_meta );
		}

            return $post_meta;
    }

    function maybe_in( $title, $post_id ) {
        $is_client = get_post_meta( $post_id, 'is_client', true );
        if ( $title == 'client' && ! $is_client ) {
            return false;
        }
        if ( $title == 'lead' && $is_client ) {
            return false;
        }
        return true;
    }

    function maybe_get_lead_data( $lead, $lead_meta ) {
        $lead_data = $lead_meta;

        if ( array_key_exists( 'person_id', $lead_meta ) ) {
            $post_ids[] = $lead_meta['person_id'][0];
        }
        if ( array_key_exists( 'org_id', $lead_meta ) ) {
            $post_ids[] = $lead_meta['org_id'][0];
        }

        foreach ( $post_ids as $post_id ) {
            $post = get_post( $post_id );
            if ( empty( $post ) ) {
				continue;}

            $post_meta = get_post_meta( $post->ID );
            $post_meta['desc'][0] = $lead->post_content;
            $lead_data = array_merge( $lead_data, $post_meta );
        }
        return $lead_data;
    }

    function set_headers( $filename ) {
        $generatedDate = date( 'd-m-Y His' );

        header( 'Pragma: public' );
        header( 'Expires: 0' );
        header( 'Cache-Control: must-revalidate, post-check=0, pre-check=0' );
        header( 'Cache-Control: private', false );
        header( 'Content-Type: text/csv; charset=utf-8' );
        header(
            'Content-Disposition: attachment; filename="' .
                $filename .
                '-' .
                $generatedDate .
                '.csv";'
        );
        header( 'Content-Transfer-Encoding: binary' );
    }

    function map_entity( $fields, $meta, $title ) {
        $new_data = [];
        $fields = array_keys( $fields );
        foreach ( $fields as $field ) {
            $field = $field == 'org_name' ? 'name' : $field;

            if ( isset( $meta[ $field ] ) ) {
                array_push( $new_data, $meta[ $field ][0] );
            } else {
                array_push( $new_data, '' );
            }
        }
        return $new_data;
    }
}

<?php

namespace Ndpvp\Api\Type;

use Ndpvp\Models\Form as ModelForm;

class Form {

    public function __construct() {
        add_action( 'rest_api_init', [ $this, 'rest_routes' ] );
    }

    public function rest_routes() {
        register_rest_route(
            'ndpv/v1', '/forms/(?P<id>\d+)', [
				'methods' => 'PUT',
				'callback' => [ $this, 'update' ],
				'permission_callback' => [ $this, 'update_permission' ],
				'args' => [
					'id' => [
						'validate_callback' => function ( $param, $request, $key ) {
							return is_numeric( $param );
						},
					],
				],
			]
        );
    }

    public function update( $req ) {
        $param = $req->get_params();
        $reg_errors = new \WP_Error();

        $form = isset( $param['form'] ) ? sanitize_text_field( $param['form'] ) : null;
        $active = isset( $param['active'] ) ? rest_sanitize_boolean( $param['active'] ) : null;
        $fields = isset( $param['fields'] ) ? $param['fields'] : null;  //TODO: sanitize
        // $fields = isset($param['fields']) ? array_map('sanitize_text_field', $param['fields']) : null;

        if ( empty( $form ) ) {
            $reg_errors->add( 'field', esc_html__( 'Form is missing', 'propovoice-pro' ) );
        }

        if ( $reg_errors->get_error_messages() ) {
            wp_send_json_error( $reg_errors->get_error_messages() );
        } else {
            $url_params = $req->get_url_params();
            $form_id    = $url_params['id'];
            $data = [];
            $data['active'] = $active;
            $data['fields'] = $fields;

            update_option( "ndpv_{$form}_{$form_id}", $data );

            wp_send_json_success();
        }
    }

    public function update_permission() {
        return current_user_can( 'edit_posts' );
    }
}

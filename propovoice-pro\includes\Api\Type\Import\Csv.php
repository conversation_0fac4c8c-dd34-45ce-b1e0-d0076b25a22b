<?php

namespace Ndpvp\Api\Type\Import;

// use Ndpvp;

class Csv {

    public $lead_meta_data = [];
    public $new_org_id;
    public $new_person_id;
    public $new_lead_id;
    public $title;
    public $data;
    public $fields;
    public $row_num;
    public $first_name_index, $org_name_index, $name_index, $email_index, $mobile_index, $desc_index, $budget_index, $currency_index;


    function import( \WP_REST_Request $request ) {
		$params = $request->get_body_params();
        $fields = explode( ',', $params['fields'] );
        $this->title = $params['title'];
        $files = $request->get_file_params();

        if ( ! empty( $files ) && ! empty( $files['file'] ) ) {
            $file = $files['file'];
            $this->insert_post( $file, $fields );

            wp_send_json( 'File imported successfully' );
        } else {
            status_header( 404 );
            $error_message = 'File not found.';
            wp_send_json_error( $error_message );
        }
    }

    function insert_post( $file, $fields ) {
        $skip_first_row = true;
        $fields = array_filter( $fields );

        // Find index of all field and save to variable
        foreach ( $fields as $field ) {
            $this->{$field . '_index'} = array_search( $field, $fields );
        }
        // Replace org_name by name
        $fields[ $this->org_name_index ] = 'name';
        $this->name_index = $this->org_name_index;

        $this->fields = $fields;

        if ( ( $handle = fopen( $file['tmp_name'], 'r' ) ) !== false ) {
            $index = 0;
            while ( ( $data = fgetcsv( $handle, 0, ',' ) ) !== false ) {
                // Remove surrounded quote and space from each field data
                $data = array_map(
                    function ( $item ) {
                        return trim( $item, ' "' );
                    }, $data
                );

                $this->data = $data;
                $this->row_num = count( $data );

                if ( $skip_first_row && $index == 0 ) {
                    ++$index;
                    continue;
                }

                // Check for required fields
                if ( empty( $this->data[ $this->first_name_index ] )
                    || empty( $this->data[ $this->email_index ] ) ) {
                    continue;
                }

                $this->lead_meta_data = [
                    'budget' => $this->data[ $this->budget_index ],
                    'currency' => $this->data[ $this->currency_index ],
                ];

                // Create new org if not exists
                $this->new_org_id = $this->is_contact_exists( 'ndpv_org' );
                if ( $this->data[ $this->org_name_index ] && ! $this->new_org_id ) {
                    $this->add_org();
                }

                // Create new person if not exists
                $this->new_person_id = $this->is_contact_exists( 'ndpv_person' );
                if ( $this->data[ $this->first_name_index ] && ! $this->new_person_id ) {
                    $this->add_person();
                }

                $this->maybe_client( $this->new_person_id );

                $this->maybe_insert_additional_lead();

                // Assign org_id and person_id to Lead
                if ( ! empty( $this->new_lead_id ) ) {
                    if ( ! empty( $this->new_org_id ) ) {
                        $this->insert_meta( $this->new_lead_id, 'org_id', $this->new_org_id );
                    }

                    if ( ! empty( $this->new_person_id ) ) {
                        $this->insert_meta( $this->new_lead_id, 'person_id', $this->new_person_id );
                    }
                }

                if ( ! empty( $this->new_person_id ) && ! empty( $this->new_org_id ) ) {
					// Assign org_id to person
                        $this->insert_meta( $this->new_person_id, 'org_id', $this->new_org_id );

					// Assign person_id to org
                        $this->insert_meta( $this->new_org_id, 'person_id', $this->new_person_id );
                }

                $this->new_person_id = null;
                $this->new_org_id = null;

                ++$index;
            }
            fclose( $handle );
        }
    }

    public function add_person() {
                    $post_data = $this->get_post_data(
                        'ndpv_person',
                        $this->data[ $this->first_name_index ]
                    );
                    $post_id = wp_insert_post( $post_data );
                    $this->new_person_id = $post_id;
                    $this->lead_meta_data = array_merge(
                        $this->lead_meta_data, [
							'person_id' => $post_id,
                        // "org_id" => null,
						]
                    );

                    $this->insert_all_row_meta( $post_id );
    }

    public function add_org() {
        $post_data = $this->get_post_data(
            'ndpv_org',
            $this->data[ $this->org_name_index ]
        );

                    $post_id = wp_insert_post( $post_data );
                    $this->new_org_id = $post_id;

                    $this->lead_meta_data = array_merge(
                        $this->lead_meta_data, [
							// "person_id" => null,
							'org_id' => $post_id,
						]
                    );

                    $this->insert_all_row_meta( $post_id );
    }

    public function insert_all_row_meta( $post_id ) {
		for ( $c = 0; $c < $this->row_num; $c++ ) {
			if (
				in_array(
                    $this->fields[ $c ], [
						'desc',
						'budget',
						'currency',
					// "note",
					]
                )
			) {
				continue;
			}

			$this->insert_meta( $post_id, $this->fields[ $c ], $this->data[ $c ] );
		}
    }


    public function is_contact_exists( $post_type ) {
		$email = $this->data[ $this->email_index ];
		$mobile = $this->data[ $this->mobile_index ];
		$args = [
            'post_type' => [ $post_type ],
            'post_status' => 'publish',
            'meta_query' => [
                'relation' => 'AND',
                [
                    'relation' => 'OR',
                    [
                        'key'     => 'email',
                        'value'   => $email,
                        'compare' => '=',
                    ],
                ],
            ],
            'fields' => 'ids',
            'posts_per_page' => 1,
		];

		if ( $mobile ) {
			$args['meta_query'][] = [
                'key'     => 'mobile',
                'value'   => $mobile,
                'compare' => '=',
			];
		}

		$posts = get_posts( $args );

		if ( $posts ) {
			$post_id = $posts[0];
			return $post_id;
		} else {
			return false;
		}
    }


    function maybe_insert_additional_lead() {
        $content = $this->data[ $this->desc_index ] ?? '';

        // Prepare meta data for custom fields
        $default_fields = [ 'first_name', 'org_name', 'email', 'mobile', 'budget', 'currency', 'desc' ];
        $custom_fields = array_diff( $this->fields, $default_fields );
        foreach ( $custom_fields as $cf ) {
            $field_data = $this->data[ $this->{$cf . '_index'} ];

            // serialize multiselect custom field
            if ( strpos( $field_data, '&' ) !== false ) {
                $value_array = explode( '&', $field_data );
                $field_data = array_map( 'sanitize_text_field', $value_array );
                // $field_data = serialize($value_array);
            }

            $this->lead_meta_data[ $cf ] = $field_data;
        }

        if ( $this->title == 'lead' ) {
            // Insert post for Lead
            $post_data = $this->get_post_data( 'ndpv_lead', 'Lead', $content );
            $post_id = wp_insert_post( $post_data );
            $this->new_lead_id = $post_id;

            $this->lead_meta_data['tab_id'] = $post_id;

            foreach ( $this->lead_meta_data as $name => $value ) {
                $this->insert_meta( $post_id, $name, $value );
            }
        }
    }

    function maybe_client( $post_id ) {
        if ( $this->title == 'client' ) {
            $this->insert_meta( $post_id, 'is_client', 1 );
        }
    }

    function get_post_data( $post_type, $title = '', $content = '' ) {
        return [
            'post_type' => $post_type,
            'post_title' => $title,
            'post_content' => $content,
            'post_status' => 'publish',
            'post_author' => get_current_user_id(),
        ];
    }

    function insert_meta( $post_id, $name, $value ) {
        if ( ! $value ) {
			return;}
        if ( is_string( $value ) ) {
			$value = sanitize_text_field( $value );
        }

        update_post_meta( $post_id, $name, $value );
    }
}

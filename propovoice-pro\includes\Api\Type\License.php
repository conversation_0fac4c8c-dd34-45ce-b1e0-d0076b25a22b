<?php

namespace Ndpvp\Api\Type;

use Ndpvp\Models\License as ModelLicense;

class License {


    // Licensing variable
    private static $store_url = 'https://www.propovoice.com';
    private static $activate_url = 'https://license.propovoice.net/wp-json/ndpv/v1/edd_actions';
    private static $product_id = 2570;
    private static $author = 'Propovoice';

    public function __construct() {
        add_action( 'admin_init', [ __CLASS__, 'license' ] );
    }

    public static function license() {
        if ( apply_filters( 'ndpv_check_license', true ) ) { //always check license
            $settings = ndpv()->get_options( 'ndpv_wage' );
            $license_key = isset( $settings['key'] ) && ! empty( $settings['key'] ) ? trim( $settings['key'] ) : null;
            $status = isset( $settings['status'] ) && ! empty( $settings['status'] ) && $settings['status'] === 'valid';
            new ModelLicense(
                static::$store_url, NDPVP_FILE, [
					'version' => NDPVP_VERSION,
					'license' => $license_key,
					'item_id' => self::$product_id,
					'author'  => self::$author,
					'url'     => home_url(),
					'beta'    => false,
					'status'  => $status,
				]
            );
        }
    }

    public function manage_licensing( $key, $type ) {
        $error = true;
        $value = $data = $message = null;
        $license_key = ! empty( $key ) ? trim( $key ) : null;

        if ( ! empty( $type ) && $type === 'activate_license' ) {
            $api_params = [
                'edd_action' => 'activate_license',
                'license'    => $license_key,
                'item_id'    => self::$product_id,
                'url'        => home_url(),
            ];
            $response = wp_remote_post(
                self::$activate_url,
                [
					'timeout' => 15,
					'sslverify' => false,
					'body' => $api_params,
                ]
            );


            if ( is_wp_error( $response ) || 200 !== wp_remote_retrieve_response_code( $response ) ) {
                $message = esc_html__( 'An error occurred, please try again.', 'propovoice-pro' );
            } else {
                $license_data = json_decode( wp_remote_retrieve_body( $response ) );

                if ( $license_data->success === false ) {
                    switch ( $license_data->error ) {
                        case 'expired':
                            $message = sprintf(
                                esc_html__( 'Your license key expired on %s.', 'propovoice-pro' ),
                                date_i18n(
                                    get_option( 'date_format' ),
                                    strtotime( $license_data->expires, current_time( 'timestamp' ) )
                                )
                            );
                            break;
                        case 'revoked':
                            $message = esc_html__( 'Your license key has been disabled.', 'propovoice-pro' );
                            break;
                        case 'missing':
                            $message = esc_html__( 'Invalid license.', 'propovoice-pro' );
                            break;
                        case 'invalid':
                        case 'site_inactive':
                            $message = esc_html__( 'Your license is not active for this URL.', 'propovoice-pro' );
                            break;
                        case 'item_name_mismatch':
                            $message = esc_html__( 'This appears to be an invalid license key for Propovoice Pro.', 'propovoice-pro' );
                            break;
                        case 'no_activations_left':
                            $message = esc_html__( 'Your license key has reached its activation limit.', 'propovoice-pro' );
                            break;
                        default:
                            $message = esc_html__( 'An error occurred, please try again.', 'propovoice-pro' );
                            break;
                    }
                }
                // Check if anything passed on a message constituting a failure
                if ( empty( $message ) ) {
                    $settings = [];
                    $settings['status'] = $license_data->license;
                    $settings['key'] = $key;
                    $settings['type'] = 'deactivate_license';
                    $settings['item_id'] = $license_data->item_id;
                    $price_id = $license_data->price_id;
                    $settings['for'] = $price_id;
                    $settings['expires'] = $license_data->expires;
                    $data = $settings;
                    update_option( 'ndpv_wage', $settings );
                    $error = false;
                    $message = esc_html__( 'License successfully activated', 'propovoice-pro' );
                }
            }
        }

        if ( ! empty( $type ) && $type === 'deactivate_license' ) {
            $api_params = [
                'edd_action' => 'deactivate_license',
                'license'    => $license_key,
                'item_id'    => self::$product_id,
                'url'        => home_url(),
            ];
            $response = wp_remote_get(
                self::$store_url, [
					'timeout' => 15,
					'sslverify' => false,
					'body' => $api_params,
				]
            );

            // Make sure there are no errors
            if ( is_wp_error( $response ) || 200 !== wp_remote_retrieve_response_code( $response ) ) {
                $message = esc_html__( 'An error occurred, please try again.', 'propovoice-pro' );
            } else {
                update_option( 'ndpv_wage', null );
                $error = false;
                $data = [];
                $data['status'] = '';
                $data['type'] = 'activate_license';
                $message = esc_html__( 'License successfully deactivated', 'propovoice-pro' );
            }
        }

        $data = [
            'msg'   => $message,
            'type'  => $type,
            'data'  => $data,
        ];
        if ( $error ) {
            wp_send_json_error( [ $message ] );
        } else {
            wp_send_json_success( $data );
        }
    }
}

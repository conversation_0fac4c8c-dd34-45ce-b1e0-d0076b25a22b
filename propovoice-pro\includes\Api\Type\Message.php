<?php

namespace Ndpvp\Api\Type;

use Ndpv\Helpers\Fns;

class Message {

    public function __construct() {
        add_action( 'rest_api_init', [ $this, 'rest_routes' ] );
    }

    public function rest_routes() {
        register_rest_route(
            'ndpvp/v1', '/messages/(?P<id>\d+)', [
				'methods' => 'GET',
				'callback' => [ $this, 'get_single' ],
				'permission_callback' => [ $this, 'get_per' ],
				'args' => [
					'id' => [
						'validate_callback' => function ( $param, $request, $key ) {
							return is_numeric( $param );
						},
					],
				],
			]
        );

        register_rest_route(
            'ndpvp/v1', '/messages' . ndpv()->plain_route(), [
				'methods' => 'GET',
				'callback' => [ $this, 'get' ],
				'permission_callback' => [ $this, 'get_per' ],
			]
        );

        register_rest_route(
            'ndpvp/v1', '/messages', [
				'methods' => 'POST',
				'callback' => [ $this, 'create' ],
				'permission_callback' => [ $this, 'create_per' ],
			]
        );

        register_rest_route(
            'ndpvp/v1', '/messages/(?P<id>\d+)', [
				'methods' => 'PUT',
				'callback' => [ $this, 'update' ],
				'permission_callback' => [ $this, 'update_per' ],
				'args' => [
					'id' => [
						'validate_callback' => function ( $param, $request, $key ) {
							return is_numeric( $param );
						},
					],
				],
			]
        );

        register_rest_route(
            'ndpvp/v1',
            '/messages/(?P<id>[0-9,]+)',
            [
                'methods' => 'DELETE',
                'callback' => [ $this, 'delete' ],
                'permission_callback' => [ $this, 'del_per' ],
                'args' => [
                    'id' => [
                        'sanitize_callback' => 'sanitize_text_field',
                    ],
                ],
            ]
        );
    }

    public function get( $req ) {
        $param = $req->get_params();

        $per_page = 10;
        $offset = 0;

        if ( isset( $param['per_page'] ) ) {
            $per_page = $param['per_page'];
        }

        if ( isset( $param['page'] ) && $param['page'] > 1 ) {
            $offset = $per_page * $param['page'] - $per_page;
        }

        $result = $data = [];

        $param = $req->get_params();
        $reg_errors = new \WP_Error();

        $module_id = isset( $param['tab_id'] ) ? sanitize_text_field( $param['tab_id'] ) : null;

        if ( $reg_errors->get_error_messages() ) {
            wp_send_json_error( $reg_errors->get_error_messages() );
        } else {
            global $wpdb;
            $tbl_prefix = 'ndpv_';
            $tbl_name = $wpdb->prefix . $tbl_prefix . 'messages';

            $query = "SELECT * FROM {$tbl_name} WHERE `module_id` = %s";
            $args = [ $module_id ];

            // If client not read team message
            if ( current_user_can( 'ndpv_client_role' ) ) {
                $query .= ' AND `receiver_type` = %s';
                $args[] = 1;
            }

            $messages = $wpdb->get_results(
                $wpdb->prepare( $query, $args )
            );

            $messages = $messages ? $messages : [];

            $prev_date = '';
            foreach ( $messages as $msg ) {
                $query_data = [];

                $user_data = get_user_by( 'ID', $msg->sender_id );
                $user = $user_data->data;
                $query_data['text'] = $msg->text;
                $query_data['id'] = $msg->id;
                $query_data['tab_id'] = $msg->module_id;
                $query_data['attachs'] = [];
                if ( $attach_ids = $msg->attach_ids ) {
                    $attach_ids = maybe_unserialize( $attach_ids );
                    $attachs = [];
                    foreach ( $attach_ids as $val ) {
                        if ( $url = wp_get_attachment_url( $val ) ) {
                            $attach_file = get_attached_file( $val );
                            $attachs[] = [
                                'url' => $url,
                                'name' => basename( $attach_file ),
                                'type' => wp_check_filetype( $attach_file )['ext'],
                            ];
                        }
                    }
                    $query_data['attachs'] = $attachs;
                }
                $query_data['img'] = Fns::gravatar( $user->user_email );
                $query_data['name'] = $user->display_name;
                $query_data['role'] = reset( $user_data->roles );
                $query_data['canEdit'] = (
                    ( $msg->sender_id === get_current_user_id() ) ||
                    ( current_user_can( 'administrator' ) || current_user_can( 'ndpv_admin' ) )
                );
                $query_data['me'] = ( $msg->sender_id === get_current_user_id() );
                if ( $msg->sender_id === get_current_user_id() ) {
                    $query_data['role_title'] = esc_html__( 'Me', 'propovoice-pro' );
                } elseif ( reset( $user_data->roles ) === 'ndpv_client_role' ) {
                        $query_data['role_title'] = esc_html__( 'Client', 'propovoice-pro' );
				} else {
					$query_data['role_title'] = ucwords( str_replace( 'ndpv_', '', reset( $user_data->roles ) ) );
                }

                $timestamp = strtotime( $msg->date );
                $formatted_date = human_time_diff( $timestamp, current_time( 'timestamp' ) ) . ' ' . esc_html__( 'ago', 'propovoice-pro' );
                $query_data['date_ago'] = $formatted_date;
                $date_time = \DateTime::createFromFormat( 'Y-m-d H:i:s', $msg->date );
                $date_only = $date_time->format( get_option( 'date_format' ) );
                $query_data['date_format'] = $date_only;

                if ( $prev_date === $date_time->format( 'Y-m-d' ) ) {
                    $query_data['date_format'] = '';
                }

                $prev_date = $date_time->format( 'Y-m-d' );

                $data[] = $query_data;
            }
        }

        $result['result'] = $data;
        $result['total'] = 0;

        wp_send_json_success( $result );
    }

    public function get_single( $req ) {
        $url_params = $req->get_url_params();
        $user_id = $url_params['id'];

        $result = $data = [];

        global $wpdb;
        $tbl_prefix = 'ndpv_';
        $tbl_name = $wpdb->prefix . $tbl_prefix . 'messages';

        $data = $wpdb->get_results(
            $wpdb->prepare(
                "SELECT * FROM $tbl_name WHERE receiver_id = %d ORDER BY date DESC",
                $user_id
            )
        );

        $result['result'] = $data;

        wp_send_json_success( $result );
    }

    public function create( $req ) {
        $param = $req->get_params();
        $reg_errors = new \WP_Error();

        $data = [];
        $tab_id = isset( $param['tab_id'] ) ? absint( $req['tab_id'] ) : null;
        $data['text'] = isset( $param['text'] ) ? sanitize_text_field( $req['text'] ) : '';
        $data['receiver_type'] = isset( $param['receiver_type'] ) ? absint( $req['receiver_type'] ) : null;
        $data['attach_ids'] = isset( $param['attach_ids'] ) ? array_map( 'absint', $param['attach_ids'] ) : null;

        if ( empty( $tab_id ) ) {
            $reg_errors->add(
                'field',
                esc_html__( 'Tab is missing', 'propovoice-pro' )
            );
        }

        if ( empty( $data['receiver_type'] ) ) {
            $reg_errors->add(
                'field',
                esc_html__( 'Receiver type is missing', 'propovoice-pro' )
            );
        }

        if ( empty( $data['text'] ) ) {
            $reg_errors->add(
                'field',
                esc_html__( 'Message is missing', 'propovoice-pro' )
            );
        }

        if ( $reg_errors->get_error_messages() ) {
            wp_send_json_error( $reg_errors->get_error_messages() );
        } else {
            $sender_id = get_current_user_id();
            $person_id = get_post_meta( $tab_id, 'person_id', true );
            $org_id = get_post_meta( $tab_id, 'org_id', true );
            $receiver_id = $person_id ? $person_id : $org_id;

            global $wpdb;
            $tbl_name = $wpdb->prefix . 'ndpv_messages';

            $data['sender_id'] = $sender_id;
            $data['receiver_id'] = $receiver_id;
            $data['module_id'] = $tab_id;
            $data['attach_ids'] = $data['attach_ids'] ? maybe_serialize( $data['attach_ids'] ) : null;
            $data['status'] = 0;
            $data['date'] = current_time( 'mysql' );

            $insert = $wpdb->insert( $tbl_name, $data );

            if ( ! is_wp_error( $insert ) ) {
                $param['id'] = $tab_id;
                do_action( 'ndpvp_webhook', 'project_discussion_add', $param );
                wp_send_json_success( $insert );
            } else {
                wp_send_json_error();
            }
        }
    }

    public function update( $req ) {
        $param = $req->get_params();
        $reg_errors = new \WP_Error();

        $data = [];
        $tab_id = isset( $param['tab_id'] ) ? absint( $req['tab_id'] ) : null;
        $data['text'] = isset( $param['text'] ) ? sanitize_text_field( $req['text'] ) : '';
        $data['receiver_type'] = isset( $param['receiver_type'] ) ? absint( $req['receiver_type'] ) : null;
        $data['attach_ids'] = isset( $param['attach_ids'] ) ? array_map( 'absint', $param['attach_ids'] ) : null;

        if ( empty( $tab_id ) ) {
            $reg_errors->add(
                'field',
                esc_html__( 'Tab is missing', 'propovoice-pro' )
            );
        }

        if ( empty( $data['text'] ) ) {
            $reg_errors->add(
                'field',
                esc_html__( 'Message is missing', 'propovoice-pro' )
            );
        }

        if ( $reg_errors->get_error_messages() ) {
            wp_send_json_error( $reg_errors->get_error_messages() );
        } else {
            $sender_id = get_current_user_id();
            $person_id = get_post_meta( $tab_id, 'person_id', true );
            $org_id = get_post_meta( $tab_id, 'org_id', true );
            $receiver_id = $person_id ? $person_id : $org_id;

            global $wpdb;

            $url_params = $req->get_url_params();
            $id = absint( $url_params['id'] );

            $tbl_name = $wpdb->prefix . 'ndpv_messages';

            $data['sender_id'] = $sender_id;
            $data['receiver_id'] = $receiver_id;
            $data['module_id'] = $tab_id;
            unset( $data['attach_ids'] );
            unset( $data['status'] );
            unset( $data['date'] );

            $update = $wpdb->update( $tbl_name, $data, [ 'id' => $id ] );

            if ( ! is_wp_error( $update ) ) {
                /* $param["id"] = $tab_id;
                do_action("ndpvp_webhook", "project_discussion_add", $param); */
                wp_send_json_success( $update );
            } else {
                wp_send_json_error();
            }
        }
    }

    public function delete( $req ) {
        $url_params = $req->get_url_params();
        global $wpdb;
        $tbl_name = $wpdb->prefix . 'ndpv_messages';

        $ids = explode( ',', $url_params['id'] );
        foreach ( $ids as $id ) {
            $wpdb->delete(
                $tbl_name,
                [ 'id' => $id ], //which id need to delete
                [ '%d' ], //make sure the id format
            );
        }
        wp_send_json_success( $ids );
    }

    // check permission
    public function get_per() {
        return $this->check_per();
    }

    public function create_per() {
        return $this->check_per();
    }

    public function update_per() {
        return $this->check_per();
    }

    public function del_per() {
        return $this->check_per();
    }

    public function check_per() {
        return (
            current_user_can( 'administrator' ) ||
            current_user_can( 'ndpv_admin' ) ||
            current_user_can( 'ndpv_manager' ) ||
            current_user_can( 'ndpv_staff' ) ||
            current_user_can( 'ndpv_client_role' )
        );
    }
}

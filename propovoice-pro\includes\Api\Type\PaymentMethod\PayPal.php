<?php

namespace Ndpvp\Api\Type\PaymentMethod;

use Exception;
use Ndpv\Models\Invoice;

class PayPal {

    public $is_live = true;
    public $base_url;
    public $access_token;
    public $headers_with_bearer;

    public function __construct() {
        $this->base_url = $this->is_live
            ? 'https://api-m.paypal.com'
            : 'https://api-m.sandbox.paypal.com';
    }

    public function get_access_token( $paypal_id ) {
        $client_id = get_post_meta( $paypal_id, 'client_id', true );
        $secret_id = get_post_meta( $paypal_id, 'secret_id', true );

        $resp = wp_remote_post(
            $this->base_url . '/v1/oauth2/token', [
				'headers' => [
					'Authorization' =>
						'Basic ' . base64_encode( $client_id . ':' . $secret_id ),
					'Content-Type' =>
						'application/x-www-form-urlencoded;charset=UTF-8',
				],
				'body' => 'grant_type=client_credentials',
			]
        );

        if ( ! is_wp_error( $resp ) ) {
            $body = (array) json_decode( $resp['body'] );
            $this->access_token = $body['access_token'];

            $this->headers_with_bearer = [
                'Authorization' => 'Bearer ' . $this->access_token,
                'Content-Type' => 'application/json',
            ];
        }
    }

    public function create_subs( $req ) {
        $param = $req->get_params();

        $invoice_id = absint( $param['id'] ) ?? '';

        $invoice = get_post_meta( $invoice_id, 'invoice', true );
        $paypal_id = $invoice['payment_methods']['paypal'] ?? '';
        $this->get_access_token( $paypal_id );

        $product_id = $this->create_product( $invoice_id );
        update_post_meta( $invoice_id, 'paypal_product_id', $product_id );

        $plan_id = $this->create_plan( $product_id, $invoice, $invoice_id );
        update_post_meta( $invoice_id, 'paypal_plan_id', $plan_id );

        wp_send_json( $plan_id );
    }

    public function save_subs_id( $req ) {
        $param = $req->get_params();

        $invoice_id = absint( $param['invoice_id'] ) ?? '';
        $subs_id = $param['subs_id'] ?? '';

        update_post_meta( $invoice_id, 'paypal_subs_id', $subs_id );
        update_post_meta( $invoice_id, 'status', 'paid' );
    }

    public function create_product( $id ) {
        $body = [
            'name' => 'Propovoice Invoice ID: ' . $id,
            'type' => 'SERVICE',
        ];

        $args = [
            'headers' => $this->headers_with_bearer,
            'body' => wp_json_encode( $body ),
        ];

        $resp = wp_remote_post(
            $this->base_url . '/v1/catalogs/products',
            $args
        );
        if ( ! is_wp_error( $resp ) ) {
            $body = (array) wp_json_encode( $resp['body'] );
            return $body['id'];
        }
    }

    public function create_plan( $product_id, $invoice, $invoice_id ) {
        $invoice_model = new Invoice();
        $amount = $invoice_model->getTotalAmount( $invoice );
        $currency = isset( $invoice['currency'] ) ? $invoice['currency'] : 'USD';

        $recurring_status = get_post_meta( $invoice_id, 'recurring', true );
        if ( $recurring_status ) {
            $recurring = $invoice['recurring'];
            $interval_type = $interval_in = $recurring['interval_type']; //day, week, month, year
            $interval = 1;
            $total_cycles = $recurring['limit_type'] ? $recurring['limit'] : 0;

            if ( $interval_type === 'custom' ) {
                $interval_in = $recurring['interval_in']; //day, week, month, year
                $interval = $recurring['interval']; //interval
            }
        }

        $billing_cycles = [
            [
                'frequency' => [
                    'interval_unit' => $interval_in,
                    'interval_count' => $interval,
                ],
                'tenure_type' => 'REGULAR',
                'sequence' => 1,
                'total_cycles' => $total_cycles,
                'pricing_scheme' => [
                    'fixed_price' => [
                        'value' => $amount,
                        'currency_code' => $currency,
                    ],
                ],
            ],
        ];

        $payment_preferences = [
            'auto_bill_outstanding' => true,
            'setup_fee' => [
                'value' => '0',
                'currency_code' => 'USD',
            ],
            'setup_fee_failure_action' => 'CONTINUE',
            'payment_failure_threshold' => 3,
        ];

        $taxes = [
            'percentage' => '0',
            'inclusive' => false,
        ];

        $body = [
            'product_id' => $product_id,
            'name' => 'Propovoice Invoice ID: ' . $invoice_id,
            'description' => 'Propovoice Invoice ID: ' . $invoice_id,
            'status' => 'ACTIVE',
            'billing_cycles' => $billing_cycles,
            'payment_preferences' => $payment_preferences,
            'taxes' => $taxes,
        ];

        $resp = wp_remote_post(
            $this->base_url . '/v1/billing/plans', [
				'headers' => $this->headers_with_bearer,
				'body' => wp_json_encode( $body ),
			]
        );

        if ( ! is_wp_error( $resp ) ) {
            $body = (array) wp_json_encode( $resp['body'] );
            return $body['id'];
        }
    }

    public function listen_webhook() {
    }
}

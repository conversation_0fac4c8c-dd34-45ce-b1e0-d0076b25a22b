<?php

namespace Ndpvp\Api\Type;

use Ndpv\Models\Invoice;

class PaymentProcess {


    public function __construct() {
        add_action( 'rest_api_init', [ $this, 'create_rest_routes' ] );
    }

    public function create_rest_routes() {

        register_rest_route(
            'ndpvp/v1', '/payment-process' . ndpv()->plain_route(), [
				'methods' => 'GET',
				'callback' => [ $this, 'get' ],
				'permission_callback' => [ $this, 'get_per' ],
			]
        );

        register_rest_route(
            'ndpvp/v1', '/payment-process', [
				'methods' => 'POST',
				'callback' => [ $this, 'create' ],
				'permission_callback' => [ $this, 'create_permission' ],
			]
        );
    }

    public function get( $req ) {
        $param = $req->get_params();

        $type = isset( $param['type'] ) ? sanitize_text_field( $param['type'] ) : '';
        $is_package = isset( $param['is_package'] ) ? true : false;

        if ( $type === 'payment_indent' ) {
            $id = isset( $param['id'] ) ? absint( $param['id'] ) : '';
            $email = isset( $param['email'] ) ? sanitize_text_field( $param['email'] ) : '';

            $currency = $amount = $secret_key = null;

            if ( ! $is_package ) {
                $invoice = get_post_meta( $id, 'invoice', true );
                $payment_methods = isset( $invoice['payment_methods'] ) ? $invoice['payment_methods'] : null;
                $currency = isset( $invoice['currency'] ) ? $invoice['currency'] : 'USD';
                $stripe_id = isset( $payment_methods['stripe'] ) ? $payment_methods['stripe'] : null;

                $invoice_model = new Invoice();
                $amount = $invoice_model->getTotalAmount( $invoice );
                $secret_key = get_post_meta( $stripe_id, 'secret_key', true );
            } else {
                $currency = get_post_meta( $id, 'currency', true );
                $amount = get_post_meta( $id, 'price', true );

                $stripe_id = null;
                $option = get_option( 'ndpv_package_payment' );
                if ( $option && $option['payment_methods'] ) {
                    if ( isset( $option['payment_methods']['stripe'] ) ) {
                        $stripe_id = $option['payment_methods']['stripe'];
                    }
                }

                $secret_key = get_post_meta( $stripe_id, 'secret_key', true );
            }

            try {
                $stripe = new \Stripe\StripeClient( $secret_key );

                $zero_decimal_currency = [
                    'BIF',
                    'CLP',
                    'DJF',
                    'GNF',
                    'JPY',
                    'KMF',
                    'KRW',
                    'MGA',
                    'PYG',
                    'RWF',
                    'UGX',
                    'VND',
                    'VUV',
                    'XAF',
                    'XOF',
                    'XPF',
                ];

                if ( ! in_array( $currency, $zero_decimal_currency ) ) {
                    $amount = ( $amount * 100 );
                }

                $payment_intents = $subscription = '';

                $pay_type = 'payment';

                $recurring = null;
                if ( ! $is_package ) {
                    $recurring = isset( $invoice['recurring'] ) ? $invoice['recurring'] : null;
                    if ( $recurring && ( $recurring['status'] && $recurring['subscription'] ) ) {
                        $pay_type = 'subscription';
                    }
                } else {
                    $recurring = get_post_meta( $id, 'recurring', true );
                    $is_recurring = get_post_meta( $id, 'is_recurring', true );
                    if ( $is_recurring ) {
                        $pay_type = 'subscription';
                    }
                }

                $payment_desc = ( $is_package ) ? esc_html__( 'Propovoice Package ID: ', 'propovoice-pro' ) . $id : esc_html__( 'Propovoice Invoice ID: ', 'propovoice-pro' ) . $id;

                if ( $pay_type === 'payment' ) {
                    $payment_intents = $stripe->paymentIntents->create(
                        [
							'amount' => $amount,
							'currency' => $currency,
							'description' => $payment_desc,
							'payment_method_types' => [ 'card' ],
						]
                    );
                } elseif ( $pay_type === 'subscription' ) {
                    $recurring_status = get_post_meta( $id, 'recurring', true );
                    if ( $recurring_status ) {
                        $interval_type = $recurring['interval_type'];

                        $customer = $stripe->customers->create(
                            [
								'email' => $email,
							]
                        );

                        $product = $stripe->products->create(
                            [
								'name' => $payment_desc,
							]
                        );

                        $price_data = [
                            'unit_amount' => $amount,
                            'currency' => $currency,
                            'recurring' => [
                                'interval' => $interval_type, //day, week, month, year
                                // 'interval_count' => 3
                                //TODO: max limit params
                            ],
                            'product' => $product->id,
                        ];

                        if ( $interval_type === 'custom' ) {
                            $interval_in = $recurring['interval_in'];
                            $interval = $recurring['interval'];
                            $price_data['recurring']['interval'] = $interval_in;
                            $price_data['recurring']['interval_count'] = $interval;
                        }

                        $price = $stripe->prices->create( $price_data );

                        $subscription = $stripe->subscriptions->create(
                            [
								'customer' => $customer->id,
								'items' => [
									[ 'price' => $price->id ],
								],
								'payment_behavior' => 'default_incomplete',
								'expand' => [ 'latest_invoice.payment_intent' ],
							]
                        );

                        if ( $subscription ) {
                            $payment_intents = $subscription->latest_invoice->payment_intent;
                        }
                    }
                }
            } catch ( \Exception $e ) {
            }

            wp_send_json_success(
                [
					'subscription_id' => $subscription ? $subscription->id : '',
					'intent_obj' => $payment_intents,
				]
            );
        }
    }

    public function create( $req ) {
        wp_send_json_success();
    }

    // check permission
    public function get_per() {
        return true;
    }

    public function create_permission() {
        return true;
        // return current_user_can('publish_posts');
    }
}

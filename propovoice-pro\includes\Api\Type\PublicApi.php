<?php

namespace Ndpvp\Api\Type;

class PublicApi {

    public function __construct() {
        add_filter(
            'rest_pre_dispatch',
            [ $this, 'capture_all_rest_api_requests' ],
            10,
            3
        );
    }

    public function capture_all_rest_api_requests( $result, $server, $req ) {
        // Get the origin of the request
        $origin = isset( $_SERVER['HTTP_ORIGIN'] ) ? $_SERVER['HTTP_ORIGIN'] : '';

        // Define an array of restricted routes

        $restricted_endpoints = [
            '/ndpv/v1/tasks' => [ 'GET', 'POST' ],
            '/ndpv/v1/tasks/(?P<id>\d+)' => [ 'GET', 'PUT' ],
            '/ndpv/v1/tasks/(?P<id>[0-9,]+)' => [ 'DELETE' ],
            '/ndpv/v1/notes' => [ 'GET', 'POST' ],
            '/ndpv/v1/notes/(?P<id>\d+)' => [ 'GET', 'PUT' ],
            '/ndpv/v1/notes/(?P<id>[0-9,]+)' => [ 'DELETE' ],
            '/ndpv/v1/files' => [ 'GET', 'POST' ],
            '/ndpv/v1/files/(?P<id>\d+)' => [ 'GET', 'PUT' ],
            '/ndpv/v1/files/(?P<id>[0-9,]+)' => [ 'DELETE' ],
            '/ndpv/v1/persons' => [ 'GET', 'POST' ],
            '/ndpv/v1/persons/(?P<id>\d+)' => [ 'GET', 'PUT' ],
            '/ndpv/v1/persons/(?P<id>[0-9,]+)' => [ 'DELETE' ],
            '/ndpv/v1/organizations' => [ 'GET', 'POST' ],
            '/ndpv/v1/organizations/(?P<id>\d+)' => [ 'GET', 'PUT' ],
            '/ndpv/v1/organizations/(?P<id>[0-9,]+)' => [ 'DELETE' ],
            '/ndpv/v1/contacts' => [ 'GET', 'POST' ],
            '/ndpv/v1/contacts/(?P<id>\d+)' => [ 'GET', 'PUT' ],
            '/ndpv/v1/contacts/(?P<id>[0-9,]+)' => [ 'DELETE' ],
            '/ndpv/v1/businesses' => [ 'GET', 'POST' ],
            '/ndpv/v1/businesses/(?P<id>\d+)' => [ 'GET', 'PUT' ],
            '/ndpv/v1/businesses/(?P<id>[0-9,]+)' => [ 'DELETE' ],
            '/ndpv/v1/emails' => [ 'GET', 'POST' ],
            '/ndpv/v1/emails/(?P<id>\d+)' => [ 'GET' ],
            '/ndpv/v1/emails/(?P<id>[0-9,]+)' => [ 'DELETE' ],
            '/ndpv/v1/media' => [ 'GET', 'POST' ],
            '/ndpv/v1/media/(?P<id>\d+)' => [ 'GET' ],
            '/ndpv/v1/media/(?P<id>[0-9,]+)' => [ 'DELETE' ],
            '/ndpv/v1/payments' => [ 'GET', 'POST' ],
            '/ndpv/v1/payments/(?P<id>\d+)' => [ 'GET', 'PUT' ],
            '/ndpv/v1/payments/(?P<id>[0-9,]+)' => [ 'DELETE' ],
            '/ndpv/v1/payment-process' => [ 'GET', 'POST' ],
            '/ndpv/v1/dashboard' => [ 'GET' ],
            '/ndpv/v1/actions' => [ 'GET', 'POST' ],
            '/ndpv/v1/actions/(?P<id>\d+)' => [ 'GET', 'PUT' ],
            '/ndpv/v1/actions/(?P<id>[0-9,]+)' => [ 'DELETE' ],
            '/ndpv/v1/taxonomies' => [ 'GET', 'POST' ],
            '/ndpv/v1/taxonomies/(?P<id>\d+)' => [ 'GET', 'PUT' ],
            '/ndpv/v1/taxonomies/(?P<id>[0-9,]+)/(?P<tax>[a-z,_]+)' => [
                'DELETE',
            ],
            '/ndpv/v1/forms' => [ 'GET' ],
            '/ndpv/v1/webhooks' => [ 'GET', 'POST' ],
            '/ndpv/v1/webhooks/(?P<id>\d+)' => [ 'GET', 'PUT' ],
            '/ndpv/v1/webhooks/(?P<id>[0-9,]+)' => [ 'DELETE' ],
            '/ndpv/v1/settings' => [ 'GET', 'POST' ],
            '/ndpv/v1/intg-form' => [ 'GET' ],
            '/ndpv/v1/intg-smtp' => [ 'GET' ],
            '/ndpvp/v1/wc-order/(?P<id>\d+)' => [ 'GET' ],
            '/ndpvp/v1/export/csv' => [ 'POST' ],
            '/ndpvp/v1/import/csv' => [ 'POST' ],
            '/ndpvp/v1/invoices/(?P<id>\d+)/paypal-subscriptions' => [ 'GET' ],
            '/ndpvp/v1/invoices/(?P<invoice_id>\d+)/paypal-subscriptions/(?P<subs_id>[a-zA-Z0-9-]+)' => [
                'GET',
            ],
            '/ndpvp/v1/paypal-webhooks' => [ 'POST' ],
            '/ndpv/v1/forms/(?P<id>\d+)' => [ 'PUT' ],
            '/ndpv/v1/pro-settings' => [ 'GET', 'POST' ],
            '/ndpvp/v1/payment-process' => [ 'GET', 'POST' ],
            '/ndpv/v1/subscriptions' => [ 'GET', 'POST' ],
            '/ndpv/v1/subscriptions/(?P<id>\d+)' => [ 'GET', 'PUT' ],
            '/ndpv/v1/subscriptions/(?P<id>[0-9,]+)' => [ 'DELETE' ],
            '/ndpv/v1/custom-fields' => [ 'GET', 'POST' ],
            '/ndpv/v1/custom-fields/(?P<id>\d+)' => [ 'GET', 'PUT' ],
            '/ndpv/v1/custom-fields/(?P<id>[0-9,]+)' => [ 'DELETE' ],
            '/ndpv/v1/intg-smtp/sendinblue' => [ 'GET', 'POST' ],
        ];

        // Remove restriction
        $restricted_endpoints = [];
        // Check if the request is coming from a different origin and has any restriction
        if (
            ! empty( $origin ) &&
            $origin != get_site_url() &&
            $this->is_route_restricted(
                $req->get_route(),
                $req->get_method(),
                $restricted_endpoints
            )
        ) {
            return new \WP_Error(
                'rest_forbidden',
                __(
                    'You are not authorized to access this resource.',
                    'propovoice'
                ),
                [ 'status' => 403 ]
            );
        }
        return $result;
    }

    public function is_route_restricted( $route, $method, $restricted_endpoints ) {
        $is_restrict = array_filter(
            $restricted_endpoints,
            function ( $v, $k ) use ( $route, $method ) {
                return preg_match( '~^' . $k . '$~', $route ) &&
                    in_array( $method, $v );
            },
            ARRAY_FILTER_USE_BOTH
        );
        return $is_restrict ? true : false;
    }
}

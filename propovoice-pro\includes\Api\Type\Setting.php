<?php

namespace Ndpvp\Api\Type;

use Ndpvp\Api\Type\License;

class Setting {


    public function __construct() {
        add_action( 'rest_api_init', [ $this, 'create_rest_routes' ] );
    }

    public function create_rest_routes() {
        register_rest_route(
            'ndpv/v1', '/pro-settings' . ndpv()->plain_route(), [
				'methods' => 'GET',
				'callback' => [ $this, 'get' ],
				'permission_callback' => [ $this, 'get_per' ],
			]
        );

        register_rest_route(
            'ndpv/v1', '/pro-settings', [
				'methods' => 'POST',
				'callback' => [ $this, 'create' ],
				'permission_callback' => [ $this, 'create_permission' ],
			]
        );
    }

    public function get( $req ) {
        $param = $req->get_params();
        $reg_errors = new \WP_Error();

        $tab = isset( $param['tab'] ) ? sanitize_text_field( $param['tab'] ) : null;

        if ( empty( $tab ) ) {
            $reg_errors->add( 'field', esc_html__( 'Tab is missing', 'propovoice-pro' ) );
        }

        if ( $reg_errors->get_error_messages() ) {
            wp_send_json_error( $reg_errors->get_error_messages() );
        } else {
            $data = [];

            if ( $tab === 'license' ) {
                $option = get_option( 'ndpv_wage' );

                if ( $option ) {
                    if ( $option['expires'] && $option['expires'] !== 'lifetime' ) {
                        // Convert the given date string to a DateTime object
                        $given_date = new \DateTime( $option['expires'] );
                        // Get the current date and time as a DateTime object
                        $current_date = new \DateTime();
                        // Compare the two dates
                        if ( $given_date < $current_date ) {
                            $option['status'] = 'expired';
                            $option['type'] = 'activate_license';
                        }
                    }
                    $data = $option;
                } else {
                    $data['key'] = '';
                    $data['type'] = 'activate_license';
                    $data['status'] = '';
                }
            }

            wp_send_json_success( $data );
        }
    }

    public function create( $req ) {
        $param = $req->get_params();
        $reg_errors = new \WP_Error();

        $tab = isset( $param['tab'] ) ? sanitize_text_field( $param['tab'] ) : null;

        if ( empty( $tab ) ) {
            $reg_errors->add( 'field', esc_html__( 'Tab is missing', 'propovoice-pro' ) );
        }

        if ( $reg_errors->get_error_messages() ) {
            wp_send_json_error( $reg_errors->get_error_messages() );
        } else {
            $data = [];

            if ( $tab === 'license' ) {
                $key = isset( $param['key'] ) ? sanitize_text_field( $param['key'] ) : null;
                $type = isset( $param['type'] ) ? sanitize_textarea_field( $param['type'] ) : null;
                $license = new License();
                $license->manage_licensing( $key, $type );
                // $option = update_option('ndpv_' . $tab, $data);
            }

            wp_send_json_success();
        }
    }

    public function get_per() {
        return true;
    }

    public function create_permission() {
        return current_user_can( 'publish_posts' );
    }
}

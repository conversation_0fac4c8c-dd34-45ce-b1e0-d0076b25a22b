<?php

namespace Ndpvp\Api\Type;

use Ndpv\Helpers\Fns;

class Staff {

    public function __construct() {
        add_action( 'rest_api_init', [ $this, 'rest_routes' ] );
    }

    public function rest_routes() {
        register_rest_route(
            'ndpvp/v1', '/staffs/(?P<id>\d+)', [
				'methods' => 'GET',
				'callback' => [ $this, 'get_single' ],
				'permission_callback' => [ $this, 'get_per' ],
				'args' => [
					'id' => [
						'validate_callback' => function ( $param, $request, $key ) {
							return is_numeric( $param );
						},
					],
				],
			]
        );

        register_rest_route(
            'ndpvp/v1', '/staffs' . ndpv()->plain_route(), [
				'methods' => 'GET',
				'callback' => [ $this, 'get' ],
				'permission_callback' => [ $this, 'get_per' ],
			]
        );

        register_rest_route(
            'ndpvp/v1', '/staffs', [
				'methods' => 'POST',
				'callback' => [ $this, 'create' ],
				'permission_callback' => [ $this, 'create_per' ],
			]
        );

        register_rest_route(
            'ndpvp/v1', '/staffs/(?P<id>\d+)', [
				'methods' => 'PUT',
				'callback' => [ $this, 'update' ],
				'permission_callback' => [ $this, 'update_per' ],
				'args' => [
					'id' => [
						'validate_callback' => function ( $param, $request, $key ) {
							return is_numeric( $param );
						},
					],
				],
			]
        );

        register_rest_route(
            'ndpvp/v1',
            '/staffs/(?P<id>[0-9,]+)/(?P<tab_id>[0-9,]+)',
            [
                'methods' => 'DELETE',
                'callback' => [ $this, 'delete' ],
                'permission_callback' => [ $this, 'del_per' ],
                'args' => [
                    'id' => [
                        'sanitize_callback' => 'sanitize_text_field',
                    ],
                    'tab_id' => [
                        'sanitize_callback' => 'sanitize_text_field',
                    ],
                ],
            ]
        );
    }

    public function get( $req ) {
        $param = $req->get_params();

        $per_page = 10;
        $offset = 0;

        if ( isset( $param['per_page'] ) ) {
            $per_page = $param['per_page'];
        }

        if ( isset( $param['page'] ) && $param['page'] > 1 ) {
            $offset = $per_page * $param['page'] - $per_page;
        }

        $tab_id = isset( $param['tab_id'] ) ? absint( $param['tab_id'] ) : null;

        $allowed_users = [];
        $check_allowed_users = false;
        if (
            $tab_id &&
            current_user_can( 'ndpv_client_role' ) &&
            ( get_post_type( $tab_id ) == 'ndpv_project' )
        ) {
            $check_allowed_users = true;
            $get_users = get_post_meta( $tab_id, '_ndpv_allowed_users', true );
            if ( ! empty( $get_users ) ) {
                $allowed_users = $get_users;
            }
        }

        $result = $data = [];

        $args = [
            'role__in' => [ 'administrator', 'ndpv_admin', 'ndpv_manager', 'ndpv_staff' ],
            'fields'  => [ 'ID', 'user_email', 'display_name' ],
        ];
        $users = get_users( $args );

        $total_data = count( $users );

        foreach ( $users as $user ) {
            $user_id = $user->id;

            if ( $check_allowed_users ) {
                if ( ! in_array( $user_id, $allowed_users ) ) {
                    continue;
                }
            }
            $info = [];
            $info['id'] = $user_id;
            $info['name'] = $user->display_name;
            $info['email'] = $user->user_email;
            $info['img'] = Fns::gravatar( $user->user_email );
            $data[] = $info;
        }

        $result['result'] = $data;
        $result['total'] = $total_data;

        wp_send_json_success( $result );
    }

    public function get_single( $req ) {
        $url_params = $req->get_url_params();
        $tab_id = $url_params['id'];

        $result = $data = [];

        $get_users = get_post_meta( $tab_id, '_ndpv_allowed_users', true );

        if ( ! empty( $get_users ) ) {
            $args = [
                'include' => $get_users,
                'fields'  => [ 'ID', 'user_email', 'display_name' ],
            ];
            $users = get_users( $args );

            foreach ( $users as $user ) {
                $info = [];
                $info['id'] = $user->id;
                $info['name'] = $user->display_name;
                $info['email'] = $user->user_email;
                $info['img'] = Fns::gravatar( $user->user_email );
                $data[] = $info;
            }
        }

        $result['result'] = $data;

        wp_send_json_success( $result );
    }

    public function create( $req ) {
        $param = $req->get_params();
        $reg_errors = new \WP_Error();

        $tab_id = isset( $param['tab_id'] )
            ? sanitize_text_field( $req['tab_id'] )
            : null;
        $user_id = isset( $param['user_id'] )
            ? absint( $req['user_id'] )
            : null;

        if ( empty( $tab_id ) ) {
            $reg_errors->add(
                'field',
                esc_html__( 'Tab id is missing', 'propovoice-pro' )
            );
        }

        if ( empty( $user_id ) ) {
            $reg_errors->add(
                'field',
                esc_html__( 'User is missing', 'propovoice-pro' )
            );
        }

        if ( $reg_errors->get_error_messages() ) {
            wp_send_json_error( $reg_errors->get_error_messages() );
        } else {
            $user_ids = [];
            $get_users = get_post_meta( $tab_id, '_ndpv_allowed_users', true );
            if ( $get_users ) {
                if ( ! in_array( $user_id, $get_users ) ) {
                    $get_users[] = $user_id;
                    update_post_meta( $tab_id, '_ndpv_allowed_users', $get_users );
                }
            } else {
                $user_ids[] = $user_id;
                update_post_meta( $tab_id, '_ndpv_allowed_users', $user_ids );
            }

            $param['id'] = $tab_id;
            $post_type = get_post_type( $tab_id );
            $param['path'] = str_replace( 'ndpv_', '', $post_type );
            do_action( 'ndpvp_webhook', $param['path'] . '_assign', $param );
            wp_send_json_success();
        }
    }

    public function update( $req ) {
        $param = $req->get_params();
        $reg_errors = new \WP_Error();
    }

    public function delete( $req ) {
        $url_params = $req->get_url_params();
        $tab_id = $url_params['tab_id'];
        $ids = explode( ',', $url_params['id'] );
        $get_users = get_post_meta( $tab_id, '_ndpv_allowed_users', true );
        if ( $get_users ) {
            $result = array_diff( $get_users, $ids );
            update_post_meta( $tab_id, '_ndpv_allowed_users', $result );
        }
        wp_send_json_success( $ids );
    }

    // check permission
    public function get_per() {
        return current_user_can( 'ndpv_workspace' );
    }

    public function create_per() {
        return current_user_can( 'ndpv_workspace' );
    }

    public function update_per() {
        return current_user_can( 'ndpv_workspace' );
    }

    public function del_per() {
        return current_user_can( 'ndpv_workspace' );
    }
}

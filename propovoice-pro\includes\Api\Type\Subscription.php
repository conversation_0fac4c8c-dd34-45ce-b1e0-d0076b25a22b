<?php

namespace Ndpvp\Api\Type;

use Ndpv\Models\Contact;
use Ndpv\Models\Org;
use Ndpv\Models\Person;

class Subscription {

    public function __construct() {
        add_action( 'rest_api_init', [ $this, 'rest_routes' ] );
    }

    public function rest_routes() {
        register_rest_route(
            'ndpv/v1', '/subscriptions/(?P<id>\d+)', [
				'methods' => 'GET',
				'callback' => [ $this, 'get_single' ],
				'permission_callback' => [ $this, 'get_per' ],
				'args' => [
					'id' => [
						'validate_callback' => function ( $param ) {
							return is_numeric( $param );
						},
					],
				],
			]
        );

        register_rest_route(
            'ndpv/v1', '/subscriptions' . ndpv()->plain_route(), [
				'methods' => 'GET',
				'callback' => [ $this, 'get' ],
				'permission_callback' => [ $this, 'get_per' ],
			]
        );

        register_rest_route(
            'ndpv/v1', '/subscriptions', [
				'methods' => 'POST',
				'callback' => [ $this, 'create' ],
				'permission_callback' => [ $this, 'create_per' ],
			]
        );

        register_rest_route(
            'ndpv/v1', '/subscriptions/(?P<id>\d+)', [
				'methods' => 'PUT',
				'callback' => [ $this, 'update' ],
				'permission_callback' => [ $this, 'update_per' ],
				'args' => [
					'id' => [
						'validate_callback' => function ( $param ) {
							return is_numeric( $param );
						},
					],
				],
			]
        );

        register_rest_route(
            'ndpv/v1', '/subscriptions/(?P<id>[0-9,]+)', [
				'methods' => 'DELETE',
				'callback' => [ $this, 'delete' ],
				'permission_callback' => [ $this, 'del_per' ],
				'args' => [
					'id' => [
						'sanitize_callback'  => 'sanitize_text_field',
					],
				],
			]
        );
    }

    public function get( $req ) {
        $param = $req->get_params();

        $per_page = 10;
        $offset = 0;

        $ref_id = isset( $param['ref_id'] ) ? absint( $param['ref_id'] ) : null;
        $s = isset( $param['text'] ) ? sanitize_text_field( $param['text'] ) : null;

        if ( isset( $param['per_page'] ) ) {
            $per_page = $param['per_page'];
        }

        if ( isset( $param['page'] ) && $param['page'] > 1 ) {
            $offset = ( $per_page * $param['page'] ) - $per_page;
        }

        $args = [
            'post_type' => 'ndpv_estinv',
            'post_status' => 'publish',
            'posts_per_page' => $per_page,
            'offset' => $offset,
        ];

        $args['meta_query'] = [
            'relation' => 'OR',
        ];

        if ( $ref_id ) {
            $args['meta_query'][] = [
                [
                    'key'     => 'subs_ref_id',
                    'value'   => $ref_id,
                    'compare' => '=',
                ],
            ];
        }

        $query = new \WP_Query( $args );
        $total_data = $query->found_posts; //use this for pagination
        $result = $data = [];

        $query_data = [];
        $query_data['id'] = $ref_id;

        $meta = get_post_meta( $ref_id );
        $query_data['start_date'] = isset( $meta['start_date'] ) ? $meta['start_date'][0] : '';
        $query_data['end_date'] = isset( $meta['end_date'] ) ? $meta['end_date'][0] : '';
        $query_data['status'] = isset( $meta['status'] ) ? $meta['status'][0] : '';
        $query_data['date'] = get_the_time( get_option( 'date_format' ) );
        $data[] = $query_data;

        while ( $query->have_posts() ) {
            $query->the_post();
            $id = get_the_ID();

            $query_data = [];
            $query_data['id'] = $id;

            $meta = get_post_meta( $id );
            $query_data['start_date'] = isset( $meta['start_date'] ) ? $meta['start_date'][0] : '';
            $query_data['end_date'] = isset( $meta['end_date'] ) ? $meta['end_date'][0] : '';
            $query_data['status'] = isset( $meta['status'] ) ? $meta['status'][0] : '';

            $query_data['date'] = get_the_time( get_option( 'date_format' ) );
            $data[] = $query_data;
        }
        wp_reset_postdata();

        $result['result'] = $data;

        $pay_info = [];
        $get_pay_info = get_post_meta( $ref_id, 'payment_info', true );

        $pay_info['subscription_id'] = isset( $get_pay_info['subscription_id'] ) ? $get_pay_info['subscription_id'] : '';

        $contact_id = get_post_meta( $ref_id, 'to', true );
        $to_type = get_post_meta( $ref_id, 'to_type', true );
        $contact_data = [];

        if ( $contact_id ) {
            $contact_data['id'] = absint( $contact_id );
            $contact_data['type'] = $to_type;
            $contact_meta = get_post_meta( $contact_id );
            $contact_data['first_name'] = isset( $contact_meta['first_name'] ) ? $contact_meta['first_name'][0] : '';
            $contact_data['org_name'] = isset( $contact_meta['name'] ) ? $contact_meta['name'][0] : '';
            $contact_data['email'] = isset( $contact_meta['email'] ) ? $contact_meta['email'][0] : '';
        }

        $result['extra'] = [
            'summary' => $pay_info,
            'to_info' => $contact_data,
        ];
        $result['total'] = $total_data;

        wp_send_json_success( $result );
    }

    public function get_single( $req ) {
        $url_params = $req->get_url_params();
        $id = $url_params['id'];
        $query_data = [];
        $query_data['id'] = absint( $id );

        $query_meta = get_post_meta( $id );
        $query_data['ws_id'] = isset( $query_meta['ws_id'] ) ? $query_meta['ws_id'][0] : '';
        $query_data['tab_id'] = isset( $query_meta['tab_id'] ) ? absint( $query_meta['tab_id'][0] ) : '';
        $query_data['budget'] = isset( $query_meta['budget'] ) ? $query_meta['budget'][0] : '';
        $query_data['currency'] = isset( $query_meta['currency'] ) ? $query_meta['currency'][0] : '';
        $query_data['note'] = isset( $query_meta['note'] ) ? $query_meta['note'][0] : '';
        $query_data['desc'] = get_post_field( 'post_content', $id );

        $query_data['person'] = null;
        $person_id = isset( $query_meta['person_id'] ) ? $query_meta['person_id'][0] : '';
        if ( $person_id ) {
            $person = new Person();
            $query_data['person'] = $person->single( $person_id, true );
        }

        $query_data['org'] = null;
        $org_id = isset( $query_meta['org_id'] ) ? $query_meta['org_id'][0] : '';
        if ( $org_id ) {
            $org = new Org();
            $query_data['org'] = $org->single( $org_id, true );
        }

        $query_data['date'] = get_the_time( get_option( 'date_format' ) );

        wp_send_json_success( $query_data );
    }

    public function create( $req ) {
        $param = $req->get_params();
        $reg_errors = new \WP_Error();

        //lead
        $first_name = isset( $param['first_name'] ) ? sanitize_text_field( $param['first_name'] ) : null;
        $org_name   = isset( $param['org_name'] ) ? sanitize_text_field( $param['org_name'] ) : null;
        $person_id = isset( $param['person_id'] ) ? absint( $param['person_id'] ) : null;
        $org_id    = isset( $param['org_id'] ) ? absint( $param['org_id'] ) : null;
        $level_id  = isset( $param['level_id'] ) ? absint( $param['level_id'] ) : null;
        $budget    = isset( $param['budget'] ) ? sanitize_text_field( $param['budget'] ) : null;
        $currency  = isset( $param['currency'] ) ? sanitize_text_field( $param['currency'] ) : null;
        $tags      = isset( $param['tags'] ) ? array_map( 'absint', $param['tags'] ) : null;
        $desc      = isset( $param['desc'] ) ? nl2br( $param['desc'] ) : '';
        $note      = isset( $param['note'] ) ? nl2br( $param['note'] ) : '';
        $img       = isset( $param['img'] ) ? absint( $param['img'] ) : null;

        if ( empty( $first_name ) && empty( $org_name ) ) {
            $reg_errors->add( 'field', esc_html__( 'Contact info is missing', 'propovoice-pro' ) );
        }

        $person = new Person();
        if ( $person_id ) {
            $person->update( $param );
        }

        if ( ! $person_id && $first_name ) {
            $person_id = $person->create( $param );
        }

        $org = new Org();
        if ( ! $person_id && $org_id ) {
            $org->update( $param );
        }

        if ( ! $org_id && $org_name ) {
            $org_id = $org->create( $param );
        }

        if ( $reg_errors->get_error_messages() ) {
            wp_send_json_error( $reg_errors->get_error_messages() );
        } else {
            //insert lead
            $data = [
                'post_type' => 'ndpv_lead',
                'post_title' => 'Lead',
                'post_content' => $desc,
                'post_status'  => 'publish',
                'post_author'  => get_current_user_id(),
            ];
            $post_id = wp_insert_post( $data );

            if ( ! is_wp_error( $post_id ) ) {
                update_post_meta( $post_id, 'ws_id', ndpv()->get_workspace() );
                update_post_meta( $post_id, 'tab_id', $post_id ); //for task, note, file

                if ( $level_id ) {
                    wp_set_post_terms( $post_id, [ $level_id ], 'ndpv_lead_level' );
                }

                if ( $person_id ) {
                    update_post_meta( $post_id, 'person_id', $person_id );
                }

                if ( $org_id ) {
                    update_post_meta( $post_id, 'org_id', $org_id );
                }

                if ( $budget ) {
                    update_post_meta( $post_id, 'budget', $budget );
                }

                if ( $currency ) {
                    update_post_meta( $post_id, 'currency', $currency );
                }

                if ( $tags ) {
                    wp_set_post_terms( $post_id, $tags, 'ndpv_tag' );
                }

                if ( $note ) {
                    update_post_meta( $post_id, 'note', $note );
                }

                do_action( 'ndpvp_webhook', 'lead_add', $param );

                wp_send_json_success( $post_id );
            } else {
                wp_send_json_error();
            }
        }
    }

    public function update( $req ) {
        $param = $req->get_params();
        $reg_errors = new \WP_Error();

        //lead
        $first_name   = isset( $param['first_name'] ) ? sanitize_text_field( $param['first_name'] ) : null;
        $org_name     = isset( $param['org_name'] ) ? sanitize_text_field( $param['org_name'] ) : null;
        $person_id    = isset( $param['person_id'] ) ? absint( $param['person_id'] ) : null;
        $org_id       = isset( $param['org_id'] ) ? absint( $param['org_id'] ) : null;
        $level_id     = isset( $param['level_id'] ) ? absint( $param['level_id'] ) : null;
        $budget       = isset( $param['budget'] ) ? sanitize_text_field( $param['budget'] ) : null;
        $currency     = isset( $param['currency'] ) ? sanitize_text_field( $param['currency'] ) : null;
        $tags         = isset( $param['tags'] ) ? array_map( 'absint', $param['tags'] ) : null;
        $desc         = isset( $param['desc'] ) ? nl2br( $param['desc'] ) : '';
        $note         = isset( $param['note'] ) ? nl2br( $param['note'] ) : '';
        $img          = isset( $param['img'] ) ? absint( $param['img'] ) : null;

        // $img = isset($contact['img']) && isset($contact['img']['id']) ? absint($contact['img']['id']) : null;

        if ( empty( $first_name ) && empty( $org_name ) ) {
            $reg_errors->add( 'field', esc_html__( 'Contact info is missing', 'propovoice-pro' ) );
        }

        /* if (!is_email($email)) {
            $reg_errors->add('email_invalid', esc_html__('Email id is not valid!', 'propovoice'));
        } */

        $person = new Person();
        if ( $person_id ) {
            $person->update( $param );
        }

        if ( ! $person_id && $first_name ) {
            $person_id = $person->create( $param );
        }

        $org = new Org();
        if ( ! $person_id && $org_id ) {
            $org->update( $param );
        }

        if ( $org_id && $org_name ) {
            $org->update( $param );
        }

        if ( ! $org_id && $org_name ) {
            $org_id = $org->create( $param );
        }

        if ( $reg_errors->get_error_messages() ) {
            wp_send_json_error( $reg_errors->get_error_messages() );
        } else {
            $url_params = $req->get_url_params();
            $post_id    = $url_params['id'];

            $data = [
                'ID'            => $post_id,
                'post_title'    => 'Lead',
                'post_content'  => $desc,
                'post_author'   => get_current_user_id(),
            ];
            $post_id = wp_update_post( $data );

            if ( ! is_wp_error( $post_id ) ) {
                if ( $level_id ) {
                    wp_set_post_terms( $post_id, [ $level_id ], 'ndpv_lead_level' );
                }

                if ( $person_id ) {
                    update_post_meta( $post_id, 'person_id', $person_id );
                }

                if ( $org_id ) {
                    update_post_meta( $post_id, 'org_id', $org_id );
                }

                if ( $budget ) {
                    update_post_meta( $post_id, 'budget', $budget );
                }

                if ( $currency ) {
                    update_post_meta( $post_id, 'currency', $currency );
                }

                if ( $tags ) {
                    wp_set_post_terms( $post_id, $tags, 'ndpv_tag' );
                }

                if ( $note ) {
                    update_post_meta( $post_id, 'note', $note );
                }

                do_action( 'ndpvp_webhook', 'lead_edit', $param );

                wp_send_json_success( $post_id );
            } else {
                wp_send_json_error();
            }
        }
    }

    public function delete( $req ) {
        //TODO: when delete lead delete task note file, if not exist in deal project
        $url_params = $req->get_url_params();
        $ids = explode( ',', $url_params['id'] );
        foreach ( $ids as $id ) {
            wp_delete_post( $id );
        }

        do_action( 'ndpvp_webhook', 'lead_del', $ids );

        wp_send_json_success( $ids );
    }

    // check permission
    public function get_per() {
        return true;
    }

    public function create_per() {
        return current_user_can( 'publish_posts' );
    }

    public function update_per() {
        return current_user_can( 'edit_posts' );
    }

    public function del_per() {
        return current_user_can( 'delete_posts' );
    }
}

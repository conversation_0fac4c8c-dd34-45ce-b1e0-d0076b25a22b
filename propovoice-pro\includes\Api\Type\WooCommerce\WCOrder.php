<?php

namespace Ndpvp\Api\Type\WooCommerce;

use Ndpv\Models\Invoice;
use Ndpvp\Api\Type\WooCommerce\WCUser;

class WCOrder {

    public function __construct() {
    }

    // Callback function for wc_payment
    public function wc_order( \WP_Rest_Request $req ) {
        $param = $req->get_params();
        $invoice_id = isset( $param['id'] ) ? absint( $param['id'] ) : '';
        $invoice_total = $this->get_invoice_total( $invoice_id );

        $order_id = get_post_meta( $invoice_id, '_ndpv_order_id', true );

        if ( ! $order_id || ! wc_get_order( $order_id ) ) {
            $order_id = $this->create_wc_order( $invoice_id, $invoice_total );
        }

        $order = wc_get_order( $order_id );
        $pay_now_url = $order->get_checkout_payment_url() . '&pay_to=ndpv';
        wp_send_json( $pay_now_url );
    }

    private function get_invoice_total( $pv_invoice_id ) {
        $invoice = get_post_meta( $pv_invoice_id, 'invoice', true );
        $invoice_model = new Invoice();
        return $invoice_model->getTotalAmount( $invoice );
    }
    private function get_addresses( $invoice_id ) {
        $contact_id = get_post_meta( $invoice_id, 'to', true );

        $contact_type = get_post_meta( $invoice_id, 'to_type' );
        $contact_addresses = get_post_meta( $contact_id );

        $first_name =
            $contact_addresses['name'][0] ??
            $contact_addresses['first_name'][0];
        $address = [
            'first_name' => $first_name,
            'last_name' => $contact_addresses['last_name'][0],
            // "company" => "azharibnmostafiz.xyz",
            'email' => $contact_addresses['email'][0],
            'phone' => $contact_addresses['mobile'][0],
            'address_1' => $contact_addresses['address'][0],
            // "address_2" => "",
            // "city" => "Tbilisi",
            // "state" => "",
            // "postcode" => "0108",
            'country' => $contact_addresses['country'][0],
        ];

        return $address;
    }
    private function create_wc_order( $invoice_id, $invoice_total ) {
        $fee = new \WC_Order_Item_Fee();
        $fee->set_name(
            sprintf(
                /* translators: %s: Invoice ID */
                esc_html__( 'Total amount for invoice id #%s', 'propovoice-pro' ),
                $invoice_id
            )
        );
        $fee->set_amount( $invoice_total );
        $fee->set_total( $invoice_total );
        $address = $this->get_addresses( $invoice_id );

        $order = wc_create_order();

        $order->set_currency(
            get_post_meta( $invoice_id, 'invoice', true )['currency'] ??
                get_woocommerce_currency()
        );
        $order->add_item( $fee );
        $order->calculate_totals();
        $order->update_meta_data( '_ndpv_invoice_id', $invoice_id );

        // Assign user to the order
        // $wc_user = new WCUser();
        // $order->set_customer_id($wc_user->get_user_id($address["email"], $address));

        $order->set_address( $address, 'billing' );
        $order->set_address( $address, 'shipping' );

        $order_id = $order->save();
        update_post_meta( $invoice_id, '_ndpv_order_id', $order->get_id() );
        return $order_id;
    }

    public function wc_order_permission() {
        return true;
    }
}

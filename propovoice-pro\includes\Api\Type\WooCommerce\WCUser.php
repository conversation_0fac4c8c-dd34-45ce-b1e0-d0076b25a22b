<?php

namespace Ndpvp\Api\Type\WooCommerce;

class WCUser {


    function get_user_id( $email, $args ) {
        return email_exists( $email ) ?: $this->create_new_user( $email, '', '', $args );
    }

    function create_new_user( $email, $username = '', $password = '', $args = [] ) {
        $user_id = wc_create_new_customer( $email, $username, $password, $args );

        // Send email to the user
        wp_new_user_notification( $user_id );

        // User auto-login
        wp_set_current_user( $user_id );
        wp_set_auth_cookie( $user_id );

        return $user_id;
    }
}

<?php

namespace Ndpvp\Cron\Type;

use Ndpv\Helpers\Fns;
use WP_Query;

class Recurring {


    public function __construct() {
        // add_action('ndpv_one_minute_event', [$this, 'handle_event']);
        add_action( 'ndpv_half_minute_event', [ $this, 'handle_event' ] );
        // add_action('ndpv_hourly_event', [$this, 'handle_event']);
        add_filter( 'ndpv_wage', [ &$this, 'wage' ] );
    }

    /**
     * Run schedule every specific time
     * If history add from last date
     * Check if any recurring on
     * Check if recurring not reached limit
     * Send me a copy or not
     * Add history if recurring sent
     */
    public function handle_event() {
        $args = [
            'post_type' => 'ndpv_estinv',
            'post_status' => 'publish',
            'posts_per_page' => -1,
        ];

        $query = new WP_Query( $args );

        while ( $query->have_posts() ) {
            $query->the_post();
            $id = get_the_ID();

            $recurring = get_post_meta( $id, 'recurring', true );
            if ( $recurring ) {
                $invoice = get_post_meta( $id, 'invoice', true );
                $recurring = $invoice['recurring'];
                $currency = $invoice['currency'];

                $date = get_post_meta( $id, 'date', true );

                if ( $date ) {
                    $current_date = date( 'Y-m-d', current_time( 'timestamp' ) );
                    $format_date = date( 'Y-m-d', strtotime( $date ) );
                    // get date from history
                    // start from history last date
                    $recurring_history = get_post_meta( $id, 'recurring_history', true );
                    if ( $recurring_history ) {
                        $get_last = end( $recurring_history );
                        $date = date( 'Y-m-d', $get_last['date_time'] );
                    }

                    $new_interval_date = $exp_interval_date = '';

                    $interval_type = $recurring['interval_type'] ?? '';

                    if ( $interval_type != 'custom' ) {
                        $new_interval_date = date( 'Y-m-d', strtotime( '+1 ' . $interval_type, strtotime( $date ) ) );
                        $exp_interval_date = date( 'Y-m-d', strtotime( '+2 ' . $interval_type, strtotime( $date ) ) );
                    } else {
                        $interval_in = $recurring['interval_in'];
                        $interval = $recurring['interval'];
                        if ( $interval ) {
                            $new_interval_date = date( 'Y-m-d', strtotime( '+' . $interval . ' ' . $interval_in, strtotime( $date ) ) );
                            $exp_interval_date = date( 'Y-m-d', strtotime( '+' . ( $interval + 1 ) . ' ' . $interval_in, strtotime( $date ) ) );
                        }
                    }

                    if ( $current_date === $new_interval_date ) {
                        $has_recurring = true;
                        if ( $recurring_history ) {
                            $limit_cross = false;
                            if ( $recurring['limit_type'] ) {
                                $limit = $recurring['limit'];
                                $sent_count = count( $recurring_history );

                                if ( $limit === $sent_count ) {
                                    $limit_cross = true;
                                }
                            }

                            if ( ! $limit_cross ) {
                                foreach ( $recurring_history as $val ) {
                                    $already_sent = date( 'Y-m-d', $val['date_time'] );
                                    if ( $current_date === $already_sent ) {
                                        $has_recurring = false;
                                        break;
                                    }
                                }
                            } else {
                                $has_recurring = false;
                            }
                        }

                        //TODO: check sent mail time range like in 10am etc
                        if ( $has_recurring ) {
                            //recurring subscription
                            if ( isset( $recurring['subscription'] ) && $recurring['subscription'] ) {
                                $inv_id = $this->copy_subs_inv( $id, $new_interval_date, $exp_interval_date );
                                if ( $inv_id ) {
                                    $this->recurring_history( $id, $inv_id );
                                }
                            } elseif ( $recurring['delivery'] ) {
                                //auto send delivery
                                    //copy invoice and get new id
                                    $inv_id = $this->copy_inv( $id );
                                    $send_mail = $this->sent_email( $id, $inv_id, $recurring, $currency );
								if ( $send_mail ) {
									$this->recurring_history( $id, $inv_id );
								}
							} else {
								$inv_id = $this->copy_inv( $id );
								if ( $inv_id ) {
									$this->recurring_history( $id, $inv_id );
								}
                            }
                        }
                    }
                }
            }
        }
        wp_reset_postdata();
    }

    public function sent_email( $id, $inv_id, $recurring, $currency ) {
        $from_id = get_post_meta( $id, 'from', true );

        $from_meta = get_post_meta( $from_id );

        $org_name = isset( $from_meta['name'] ) ? $from_meta['name'][0] : '';
        $mail_from = isset( $from_meta['email'] ) ? $from_meta['email'][0] : '';

        $org_img = '';
        $org_address = '';
        if ( $from_id ) {
            $query_meta = get_post_meta( $from_id );
            $logo_id = isset( $query_meta['logo'] ) ? $query_meta['logo'][0] : '';
            $address = isset( $query_meta['address'] )
                ? $query_meta['address'][0]
                : '';
            $email = isset( $query_meta['email'] ) ? $query_meta['email'][0] : '';
            $mobile = isset( $query_meta['mobile'] )
                ? $query_meta['mobile'][0]
                : '';

            if ( $logo_id ) {
                $logo_src = wp_get_attachment_image_src( $logo_id, 'thumbnail' );
                if ( $logo_src ) {
                    $org_img = "<img src='" . $logo_src[0] . "' alt='' style='max-width: 200px !important;max-height: 90px !important;'/>";
                }
            }

            if ( $address ) {
                $org_address .= $address . '<br />';
            }
            $org_address .= $email;
            if ( $mobile ) {
                $org_address .= ',<br />' . $mobile;
            }
        }

        $to_id = get_post_meta( $id, 'to', true );
        $to_type = get_post_meta( $id, 'to_type', true );
        $to_meta = get_post_meta( $to_id );

        $mail_to = isset( $to_meta['email'] ) ? $to_meta['email'][0] : '';

        $client_name = '';
        if ( $to_type === 'person' ) {
            $client_name = isset( $to_meta['first_name'] ) ? $to_meta['first_name'][0] : '';
        } else {
            $client_name = isset( $to_meta['name'] ) ? $to_meta['name'][0] : '';
        }

        $path = get_post_meta( $id, 'path', true );

        $amount = get_post_meta( $id, 'total', true );
        if ( $amount ) {
            $amount .= ' ' . $currency;
        }

        $data = [];

        $option = get_option( 'ndpv_email_invoice_recurring' );
        if ( $option ) {
            $data = $option;
        } else {
            $data['subject'] = ndpv()->get_default( 'email_template', 'invoice', 'recurring', 'subject' );
            $data['msg'] = ndpv()->get_default( 'email_template', 'invoice', 'recurring', 'msg' );
        }

        $mail_subject = $data['subject'];
        $msg = nl2br( $data['msg'] );

        $token = get_post_meta( $id, 'token', true );
        $title = ( $path === 'invoice' ) ? esc_html__( 'Invoice', 'propovoice-pro' ) : esc_html__( 'Estimate', 'propovoice-pro' );

        $date = get_post_meta( $inv_id, 'date', true );
        $date = date( 'Y-m-d', strtotime( $date ) );

        $due_date = get_post_meta( $inv_id, 'due_date', true );
        $due_date = date( 'Y-m-d', strtotime( $due_date ) );

        $url = sprintf(
            '%s?id=%s&token=%s',
            Fns::client_page_url( $path ),
            $inv_id,
            $token
        );

        $subject = Fns::templateVariable(
            $mail_subject, [
				'id' => $inv_id,
				'org_name' => $org_name,
				'client_name' => $client_name,
			]
        );

        $template = ndpv()->render( 'email/invoice', [], true );

        $msg = Fns::templateVariable(
            $msg, [
				'id' => $inv_id,
				'path' => $path,
				'org_name' => $org_name,
				'client_name' => $client_name,
				'date' => $date,
				'due_date' => $due_date,
				'amount' => $amount,
			]
        );

        $body = Fns::templateVariable(
            $template, [
				'msg' => $msg,
				'url' => $url,
				'path' => $path,
				'title' => $title,
				'org_name' => $org_name,
				'org_img' => $org_img,
				'org_address' => $org_address,
			]
        );

        $headers = [ 'Content-Type: text/html; charset=UTF-8' ];
        $headers[] = 'From: ' . $org_name . ' <' . $mail_from . '>';
        //TODO: dynamic Cc later
        //$headers[] = 'Cc: Rakib <<EMAIL>>';
        if ( $recurring['send_me'] ) { //TODO: check send me
            $headers[] = 'Cc: ' . $mail_from;
        }
        //$headers[] = 'Cc: <EMAIL>'; // note you can just use a simple email address

        //attachment
        $attachments = [];
        return wp_mail( $mail_to, $subject, $body, $headers, $attachments );
    }

    public function copy_inv( $id ) {
        $title = get_the_title( $id );
        $post = [
            'post_title' => $title,
            'post_status' => 'publish',
            'post_type' => 'ndpv_estinv',
            'post_author' => get_current_user_id(),
        ];
        $new_post_id = wp_insert_post( $post );

        update_post_meta( $new_post_id, 'ref_id', $id );

        // Copy post metadata
        $data = get_post_meta( $id );
        foreach ( $data as $key => $values ) {
            foreach ( $values as $value ) {
                if ( $key === 'status' ) {
                    $value = 'draft';
                }

                $current_date = date( 'Y-m-d', current_time( 'timestamp' ) );
                $month_due_date = strtotime( '+1 month', strtotime( $current_date ) );
                $new_due_date = date( 'Y-m-d', $month_due_date );

                if ( $key === 'date' ) {
                    $value = $current_date;
                }

                if ( $key === 'due_date' ) {
                    $value = $new_due_date;
                }

                if ( $key === 'invoice' ) {
                    $value = maybe_unserialize( $value );
                    $value['recurring'] = null;
                    $value['date'] = $current_date;
                    $value['due_date'] = $new_due_date;
                }
                update_post_meta( $new_post_id, $key, maybe_unserialize( $value ) );
            }
        }
        return $new_post_id;
    }

    public function copy_subs_inv( $id, $start_date, $end_date ) {
        $title = get_the_title( $id );
        $post = [
            'post_title' => $title,
            'post_status' => 'publish',
            'post_type' => 'ndpv_estinv',
            'post_author' => get_current_user_id(),
        ];
        $new_post_id = wp_insert_post( $post );

        update_post_meta( $new_post_id, 'ref_id', $id );
        update_post_meta( $new_post_id, 'subs_ref_id', $id );
        update_post_meta( $new_post_id, 'start_date', $start_date );
        update_post_meta( $new_post_id, 'end_date', $end_date );

        return $new_post_id;
    }

    public function recurring_history( $id, $inv_id ) {
        $recurring_history = get_post_meta( $id, 'recurring_history', true );
        if ( $recurring_history ) {
            $recurring_history[] = [
                'id' => $inv_id,
                'date_time' => current_time( 'timestamp' ),
                'time_zone' => '',
            ];
            update_post_meta( $id, 'recurring_history', $recurring_history );
        } else {
            $recurring_history = [
                [
                    'id' => $inv_id,
                    'date_time' => current_time( 'timestamp' ),
                    'time_zone' => '',
                ],
            ];
            update_post_meta( $id, 'recurring_history', $recurring_history );
        }
    }

    public function wage( $default ) {
		if ( function_exists( 'ndpvp' ) && ndpvp()->wage() ) {
			return apply_filters( 'ndpvp_wage', [] );
		}
		return $default;
	}
}

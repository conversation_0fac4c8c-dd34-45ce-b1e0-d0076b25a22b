<?php

namespace Ndpvp\Cron\Type;

use Ndpv\Helpers\Fns;
use WP_Query;

class Reminder {


    public function __construct() {
        add_action( 'ndpv_one_minute_event', [ $this, 'handle_event' ] );
        // add_action('ndpv_half_minute_event', [$this, 'handle_event']);
        // add_action('ndpv_hourly_event', [$this, 'handle_event']);
    }

    /**
     * Run schedule every specific time
     * Check not paid module
     * Check if any reminder on
     * Check if reminder not reached limit
     * Add history if reminder sent
     */
    public function handle_event() {
        $args = [
            'post_type' => 'ndpv_estinv',
            'post_status' => 'publish',
            'posts_per_page' => -1,
        ];

        $query = new WP_Query( $args );

        while ( $query->have_posts() ) {
            $query->the_post();
            $id = get_the_ID();

            $status = get_post_meta( $id, 'status', true );
            if ( $status && ( $status !== 'paid' || $status !== 'accept' || $status !== 'decline' ) ) {
                $reminder = get_post_meta( $id, 'reminder', true );
                if ( $reminder ) {
                    $invoice = get_post_meta( $id, 'invoice', true );
                    $reminder = $invoice['reminder'];
                    $currency = $invoice['currency'];

                    $check_history = false;
                    $due_date = get_post_meta( $id, 'due_date', true );

                    if ( $due_date ) {
                        $current_date = date( 'Y-m-d', current_time( 'timestamp' ) );
                        $format_due_date = date( 'Y-m-d', strtotime( $due_date ) );

                        $reminder_due_date = $reminder['due_date'];
                        if ( $reminder_due_date ) {
                            if ( $current_date === $format_due_date ) {
                                $check_history = $current_date;
                            }
                        }

                        $before_date = $reminder['before'] ?? [];
                        if ( $before_date ) {
                            foreach ( $before_date as $val ) {
                                $before_date_number = strtotime( '-' . $val . ' day', strtotime( $due_date ) );
                                $new_before_before = date( 'Y-m-d', $before_date_number );

                                if ( $current_date === $new_before_before ) {
                                    $check_history = $current_date;
                                    break;
                                }
                            }
                        }

                        $after_date = $reminder['after'] ?? [];
                        if ( $after_date ) {
                            foreach ( $after_date as $val ) {
                                $after_date_number = strtotime( '+' . $val . ' day', strtotime( $due_date ) );
                                $new_after_after = date( 'Y-m-d', $after_date_number );

                                if ( $current_date === $new_after_after ) {
                                    $check_history = $current_date;
                                    break;
                                }
                            }
                        }

                        if ( $check_history ) {
                            $reminder_history = get_post_meta( $id, 'reminder_history', true );
                            $sent_mail = true;
                            if ( $reminder_history ) {
                                foreach ( $reminder_history as $val ) {
                                    $already_sent = date( 'Y-m-d', $val['date_time'] );
                                    if ( $current_date === $already_sent ) {
                                        $sent_mail = false;
                                        break;
                                    }
                                }
                            }

                            //sent mail
                            //TODO: check sent mail time range like in 10am etc
                            if ( $sent_mail ) {
                                $this->sent_email( $id, $currency );
                            }
                        }
                    }
                }
            }
        }
        wp_reset_postdata();
    }

    public function sent_email( $id, $currency ) {
        $from_id = get_post_meta( $id, 'from', true );

        //TODO: sent to by name or company setting
        $from_meta = get_post_meta( $from_id );

        //TODO: sent to by name or company setting
        $company_name = isset( $from_meta['name'] ) ? $from_meta['name'][0] : '';
        $mail_from = isset( $from_meta['email'] ) ? $from_meta['email'][0] : '';

        $to_id = get_post_meta( $id, 'to', true );
        $to_type = get_post_meta( $id, 'to_type', true );
        $to_meta = get_post_meta( $to_id );

        $mail_to = isset( $to_meta['email'] ) ? $to_meta['email'][0] : '';

        $client_name = '';
        if ( $to_type === 'person' ) {
            $client_name = isset( $to_meta['first_name'] ) ? $to_meta['first_name'][0] : '';
        } else {
            $client_name = isset( $to_meta['name'] ) ? $to_meta['name'][0] : '';
        }

        $path = get_post_meta( $id, 'path', true );

        $date = get_post_meta( $id, 'date', true );
        $date = date( 'Y-m-d', strtotime( $date ) );

        $due_date = get_post_meta( $id, 'due_date', true );
        $due_date = date( 'Y-m-d', strtotime( $due_date ) );

        $amount = get_post_meta( $id, 'total', true );

        if ( $amount ) {
            $amount .= ' ' . $currency;
        }

        $data = [];

        if ( $path === 'estimate' ) {
            $option = get_option( 'ndpv_email_estimate_reminder' );

            if ( $option ) {
                $data = $option;
            } else {
                $data['subject'] = ndpvp()->get_default( 'email_template', 'estimate', 'reminder', 'subject' );
                $data['msg'] = ndpvp()->get_default( 'email_template', 'estimate', 'reminder', 'msg' );
            }
        }

        if ( $path === 'invoice' ) {
            $option = get_option( 'ndpv_email_invoice_reminder' );
            if ( $option ) {
                $data = $option;
            } else {
                $data['subject'] = ndpvp()->get_default( 'email_template', 'invoice', 'reminder', 'subject' );
                $data['msg'] = ndpvp()->get_default( 'email_template', 'invoice', 'reminder', 'msg' );
            }
        }

        $mail_subject = $data['subject'];
        $msg = nl2br( $data['msg'] );

        $token = get_post_meta( $id, 'token', true );
        $url = sprintf(
            '%s?id=%s&token=%s',
            Fns::client_page_url( $path ),
            $id,
            $token
        );

        $subject = Fns::templateVariable(
            $mail_subject, [
				'id' => $id,
				'company_name' => $company_name,
				'client_name' => $client_name,
			]
        );

        $template = ndpv()->render( 'email/invoice', [], true );

        $msg = Fns::templateVariable(
            $msg, [
				'id' => $id,
				'path' => $path,
				'company_name' => $company_name,
				'client_name' => $client_name,
				'date' => $date,
				'due_date' => $due_date,
				'amount' => $amount,
			]
        );

        $body = Fns::templateVariable(
            $template, [
				'msg' => $msg,
				'url' => $url,
				'path' => $path,
			]
        );

        $headers = [ 'Content-Type: text/html; charset=UTF-8' ];
        $headers[] = 'From: ' . $company_name . ' <' . $mail_from . '>';
        //TODO: dynamic Cc later
        //$headers[] = 'Cc: Rakib <<EMAIL>>';
        //$headers[] = 'Cc: <EMAIL>'; // note you can just use a simple email address

        //attachment
        $attachments = [];
        $send_mail = wp_mail( $mail_to, $subject, $body, $headers, $attachments );

        if ( $send_mail ) {
            $reminder_history = get_post_meta( $id, 'reminder_history', true );
            if ( $reminder_history ) {
                $reminder_history[] = [
                    'date_time' => current_time( 'timestamp' ),
                    'time_zone' => '',
                ];
                update_post_meta( $id, 'reminder_history', $reminder_history );
            } else {
                $reminder_history = [
                    [
                        'date_time' => current_time( 'timestamp' ),
                        'time_zone' => '',
                    ],
                ];
                update_post_meta( $id, 'reminder_history', $reminder_history );
            }
        }
    }
}

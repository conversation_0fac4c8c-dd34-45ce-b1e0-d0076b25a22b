<?php

namespace Ndpvp\Helpers;

class Constant {

    public function __construct() {
        if ( ! defined( 'NDPVP_VERSION' ) ) {
            define( 'NDPVP_VERSION', '1.7.1.4' );
        }

        if ( ! defined( 'NDPVP_PATH' ) ) {
            define( 'NDPVP_PATH', plugin_dir_path( NDPVP_FILE ) );
        }

        if ( ! defined( 'NDPVP_URL' ) ) {
            define( 'NDPVP_URL', plugins_url( '', NDPVP_FILE ) );
        }

        if ( ! defined( 'NDPVP_SLUG' ) ) {
            define( 'NDPVP_SLUG', basename( dirname( NDPVP_FILE ) ) );
        }

        if ( ! defined( 'NDPVP_TEMPLATE_DEBUG_MODE' ) ) {
            define( 'NDPVP_TEMPLATE_DEBUG_MODE', false );
        }
    }
}

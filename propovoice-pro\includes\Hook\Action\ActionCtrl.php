<?php

namespace Ndpvp\Hook\Action;

use Ndpvp\Hook\Action\Type\Common;
use Ndpvp\Hook\Action\Type\Webhook;
use Ndpvp\Hook\Action\Type\RestRoute;
use Ndpvp\Hook\Action\Type\FormSubmit;
use Ndpvp\Hook\Action\Type\Mailer;
use Ndpvp\Hook\Action\Type\WC;
use Ndpvp\Hook\Action\Type\Roles;
use Ndpvp\Hook\Action\Type\Notifications;

class ActionCtrl {

    public function __construct() {
        new RestRoute();
        new Webhook();
        new Mailer();
        new FormSubmit();
        new WC();
        new Roles();
        new Common();
        new Notifications();
    }
}

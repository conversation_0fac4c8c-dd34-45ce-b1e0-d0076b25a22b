<?php
declare(strict_types=1);

namespace Ndpvp\Hook\Action\Type;

use Ndpvp\Models\ActivitiesModel;
use Ndpvp\Models\NotificationsModel;
use Ndpv\Helpers\Fns;

class Activities {

    public function __construct() {
    }

    /**
     *
     */
    public function manage_activity( string $type, array $param ): void {
        $args = [];

        $post_id = $param['id'] ?? null;
        $by = wp_get_current_user()->display_name;

        $title = '';
        $path = $param['path'] ?? '';
        $is_notification = true;

        switch ( $type ) {
            case 'lead_add':
                $title = sprintf( __( '<b>Lead added</b> by <b> %s </b>', 'propovoice-pro' ), $by );
                $path = 'lead';
                break;
            case 'lead_level_change':
                $title = sprintf( __( '<b>Lead level changed</b> by <b> %s </b>', 'propovoice-pro' ), $by );
                $path = 'lead';
                break;
            case 'deal_add':
                $title = sprintf( __( '<b>Deal added</b> by <b> %s </b>', 'propovoice-pro' ), $by );
                $path = 'deal';
                break;

            case 'deal_stage_change':
                $title = sprintf( __( '<b>Deal stage changed</b> by <b> %s </b>', 'propovoice-pro' ), $by );
                $path = 'deal';
                break;

            case 'lead_to_deal':
                $title = sprintf( __( '<b>Lead moved to Deal</b> by <b> %s </b>', 'propovoice-pro' ), $by );
                $path = 'deal';
                break;

            case 'task_add':
                $title = sprintf( __( '<b>Task added</b> by <b> %s </b>', 'propovoice-pro' ), $by );
                $path = 'task';
                break;

                // Task edit giving too much notification
                // Should make change in task edit system

                // case "task_edit":
                //     $title =  sprintf(__("Task updated by <b> %s </b>"), "propovoice-pro"), $by);
                //     $path = "task";
                //     break;

            case 'taxonomy' && isset( $param['taxonomy'] ) && $param['taxonomy'] == 'task_status':
                $title = sprintf( __( '<b>Task status changed</b> by <b> %s </b>', 'propovoice-pro' ), $by );
                $path = 'task';
                $type = 'task_status_change';
                $post_id = $param['post_id'];
                break;

            case 'file_add':
                $title = sprintf( __( '<b>File added</b> by <b> %s </b>', 'propovoice-pro' ), $by );
                $path = 'file';
                break;

            case 'est_add':
                $title = sprintf( __( '<b>Estimate added</b> by <b> %s </b>', 'propovoice-pro' ), $by );
                $path = 'estimate';
                break;

            case 'est_edit':
                $title = sprintf( __( '<b>Estimate updated</b> by <b> %s </b>', 'propovoice-pro' ), $by );
                $path = 'estimate';
                break;
            case 'est_accept':
                $title = sprintf( __( '<b>Estimate Accepted</b>', 'propovoice-pro' ), $by );
                $path = 'estimate';
                break;
            case 'est_reject':
                $title = sprintf( __( '<b>Estimate rejected</b>', 'propovoice-pro' ), $by );
                $path = 'estimate';
                break;

            case 'inv_add':
                $title = sprintf( __( '<b>Invoice added</b> by <b> %s </b>', 'propovoice-pro' ), $by );
                $path = 'invoice';
                break;

            case 'inv_edit':
                $title = sprintf( __( '<b>Invoice updated</b> by <b> %s </b>', 'propovoice-pro' ), $by );
                $path = 'invoice';
                break;
            case 'inv_paid':
                $title = sprintf( __( '<b>Invoice paid</b> by <b> %s </b>', 'propovoice-pro' ), $by );
                $path = 'invoice';
                break;

            case 'inv_paid_req':
                $title = sprintf( __( '<b>Invoice Payment submitted</b> by <b> %s </b>', 'propovoice-pro' ), $by );
                $path = 'invoice';
                break;

            case 'inv_reject':
                $title = sprintf( __( '<b>Invoice rejected</b> by <b> %s </b>', 'propovoice-pro' ), $by );
                $path = 'invoice';
                break;

            case 'project_add':
                $title = sprintf( __( '<b>Project added</b> by <b> %s </b>', 'propovoice-pro' ), $by );
                $path = 'project';
                break;

            case 'project_discussion_add':
                $title = sprintf( __( '<b>Project Discussion added</b> by <b> %s </b>', 'propovoice-pro' ), $by );
                $path = 'project';
                break;

            case 'staff_add':
                $title = sprintf( __( '<b>Team member added </b> by <b> %s </b>', 'propovoice-pro' ), $by );
                break;

            case $path . '_assign':
                $title = sprintf( __( '<b>Team member assigned to %1$s </b> by <b> %2$s </b>', 'propovoice-pro' ), esc_html( $path ), $by );
                break;

            case 'order_add':
                $title = sprintf( __( '<b>Order added</b> by <b> %s </b>', 'propovoice-pro' ), $by );
                $path = 'order';
                break;
            case 'request_add':
                $title = sprintf( __( '<b>Request added</b> by <b> %s </b>', 'propovoice-pro' ), $by );
                $path = 'request';
                break;
            default:
                $is_notification = false;
                break;
        }

        if ( $is_notification ) {
            $permalink = Fns::client_page_url( 'workspace' );

            $action_slug = $type;

            // null for estimate accept, reject
            $created_by = get_current_user_id();
            if ( ! $created_by ) {
				$created_by = null;}

            if ( $type == 'staff_add' ) {
				$message = $title;
            } else {
				$message = "<a href='$permalink#/$path/$post_id' >$title</a>";
            }

            $args = [
                'message' => $message,
                'post_id' => $post_id,
                'action_slug' => $action_slug,
                'created_by' => $created_by,
            ];

            $extra_args = [
                'title' => $title,
                'by' => $by,
            ];

            if ( $args ) {
                $activities = new ActivitiesModel();
                $new_activities = $activities->insert_activity( $args );

                $notification = new NotificationsModel();
                $notification->manage_notification( $new_activities, $extra_args );
            }
        }
    }
}

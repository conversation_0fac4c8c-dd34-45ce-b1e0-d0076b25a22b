<?php

namespace Ndpvp\Hook\Action\Type;

class Common {

    public function __construct() {
        add_action( 'wp_login_failed', [ $this, 'wp_login_failed' ] );
    }

    public function wp_login_failed( $username ) {
        $referrer = $_SERVER['HTTP_REFERER'];  // where did the post submission come from?
        // if there's a valid referrer, and it's not the default log-in screen
        if ( ! empty( $referrer ) && ! strstr( $referrer, 'wp-login' ) && ! strstr( $referrer, 'wp-admin' ) ) {
            wp_redirect( $referrer . '?login=failed' );  // let's append some information (login=failed) to the URL for the theme to use
            exit;
        }
    }
}

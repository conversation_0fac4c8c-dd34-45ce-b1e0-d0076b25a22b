<?php

namespace Ndpvp\Hook\Action\Type;

use Ndpv\Models\Lead;

class FormSubmit {

    public function __construct() {
        add_action( 'wpcf7_submit', [ $this, 'contact_form_7' ], 999, 2 );
        add_action( 'wpforms_process_complete', [ $this, 'wpforms' ], 999, 4 );
        add_action( 'nf_save_sub', [ $this, 'ninja_forms' ], 999, 1 );
        add_action( 'gform_after_submission', [ $this, 'gravity_forms' ], 999, 2 );
        add_action( 'fluentform_before_insert_submission', [ $this, 'fluent_forms' ], 999, 1 );
        add_action( 'metform_after_store_form_data', [ $this, 'metform' ], 999, 4 );
        add_action( 'forminator_form_after_save_entry', [ $this,'forminator' ], 999, 2 );
    }

    protected function get_key( $fields, $tag ) {
        $key = '';
        if ( $fields ) {
            foreach ( $fields as $svalue ) {
                if ( $svalue['id'] == $tag ) {
                    $key = $svalue['value'];
                    break;
                }
            }
        }
        return $key;
    }

    public function forminator( $form_id, $response ) {
        $get_data = get_option( "ndpv_forminator_{$form_id}" );
        $active = isset( $get_data['active'] ) ? $get_data['active'] : false;

        if ( ! $active ) {
            return;
        }

        $get_fields = $get_data['fields'];
        $data = [];

        foreach ( $_POST as $key => $value ) {
            $field = $this->get_key( $get_fields, $key );
            if ( $key ) {
                $data[ $field ] = $value;
            }
        }

        if ( ! empty( $data ) ) {
            $lead = new Lead();
            $lead->create( $data );
        }
    }

    public function metform( $form_id, $form_data, $form_settings, $attributes ) {
        $get_data = get_option( "ndpv_metform_{$form_id}" );
        $active = isset( $get_data['active'] ) ? $get_data['active'] : false;

        if ( ! $active ) {
            return;
        }

        $get_fields = $get_data['fields'];
        $data = [];

        foreach ( $form_data as $key => $value ) {
            $field = $this->get_key( $get_fields, $key );
            if ( $key ) {
                $data[ $field ] = $value;
            }
        }

        if ( ! empty( $data ) ) {
            $lead = new Lead();
            $lead->create( $data );
        }
    }

    public function contact_form_7( $wpcf7, $result ) {
        if ( ! empty( $result['invalid_fields'] ) ) {
            return;
        }

        $form_id = $wpcf7->id();

        $get_data = get_option( "ndpv_contact_form_7_{$form_id}" );
        $active = isset( $get_data['active'] ) ? $get_data['active'] : false;

        if ( ! $active ) {
            return;
        }

        $get_fields = $get_data['fields'];

        $form_tags   = $wpcf7->scan_form_tags();
        $submission  = \WPCF7_Submission::get_instance();
        $posted_data = $submission->get_posted_data();

        $data = [];

        foreach ( $form_tags as $form_tag ) {
            $tag = $form_tag->name;

            if ( ! empty( $tag ) && isset( $posted_data[ $tag ] ) ) {
                $key = $this->get_key( $get_fields, $tag );
                if ( $key ) {
                    $data[ $key ] = $posted_data[ $tag ];
                }
            }
        }

        if ( ! empty( $data ) ) {
            $lead = new Lead();
            $lead->create( $data );
        }
    }

    public function wpforms( $fields, $entry, $form_data, $entry_id ) {
        $form_id = absint( $form_data['id'] );

        $get_data = get_option( "ndpv_wpforms_{$form_id}" );
        $active = isset( $get_data['active'] ) ? $get_data['active'] : false;

        if ( ! $active ) {
            return;
        }

        $get_fields = $get_data['fields'];
        $data = [];

        foreach ( $fields as $field ) {
            $key = $this->get_key( $get_fields, $field['id'] );
            if ( $key ) {
                $data[ $key ] = $field['value'];
            }
        }

        if ( ! empty( $data ) ) {
            $lead = new Lead();
            $lead->create( $data );
        }
    }

    public function ninja_forms( $sub_id ) {
        $submission = Ninja_Forms()->form()->get_sub( $sub_id );

        $form_id = absint( $submission->get_form_id() );

        $get_data = get_option( "ndpv_ninja_forms_{$form_id}" );
        $active = isset( $get_data['active'] ) ? $get_data['active'] : false;

        if ( ! $active ) {
            return;
        }

        $get_fields = $get_data['fields'];
        $data = [];

        $field_values = $submission->get_field_values();

        foreach ( $field_values as $key => $value ) {
            preg_match( '/_field_(\d+)/', $key, $matches );

            if ( ! empty( $matches ) && ! empty( $matches[1] ) ) {
                $field_id = intval( $matches[1] );

                $key = $this->get_key( $get_fields, $field_id );
                if ( $key ) {
                    $data[ $key ] = $value;
                }
            }
        }

        if ( ! empty( $data ) ) {
            $lead = new Lead();
            $lead->create( $data );
        }
    }

    public function gravity_forms( $lead, $form ) {
        $form_id = $lead['form_id'];

        $get_data = get_option( "ndpv_gravity_forms_{$form_id}" );
        $active = isset( $get_data['active'] ) ? $get_data['active'] : false;

        if ( ! $active ) {
            return;
        }

        $get_fields = $get_data['fields'];
        $data = [];

        foreach ( $form['fields'] as $field ) {
            $field = \GF_Fields::create( $field );

            if ( empty( $field['inputs'] ) ) {
                $key = $this->get_key( $get_fields, $field->id );
                if ( $key ) {
                    $data[ $key ] = $lead[ $field->id ];
                }
            } else {
                foreach ( $field['inputs'] as $group_field ) {
                    if ( empty( $group_field['isHidden'] ) ) {
                        $key = $this->get_key( $get_fields, $group_field['id'] );
                        if ( $key ) {
                            $data[ $key ] = $lead[ $group_field['id'] ];
                        }
                    }
                }
            }
        }

        if ( ! empty( $data ) ) {
            $lead = new Lead();
            $lead->create( $data );
        }
    }

    public function fluent_forms( $data ) {
        $form_id = $data['form_id'];

        $get_data = get_option( "ndpv_fluent_forms_{$form_id}" );
        $active = isset( $get_data['active'] ) ? $get_data['active'] : false;

        if ( ! $active ) {
            return;
        }

        $get_fields = $get_data['fields'];
        $submission = $this->fluent_submissions( json_decode( $data['response'], true ), $form_id );

        $form_data = [];
        foreach ( $submission as $key => $value ) {
            $new_key = $this->get_key( $get_fields, $key );
            if ( $new_key ) {
                $form_data[ $new_key ] = $value;
            }
        }

        if ( ! empty( $form_data ) ) {
            $lead = new Lead();
            $lead->create( $form_data );
        }
    }

    protected function fluent_submissions( $resp, $form_id ) {
        foreach ( [ '__fluent_form_embded_post_id', '_fluentform_' . $form_id . '_fluentformnonce', '_wp_http_referer' ] as $field ) {
            if ( array_key_exists( $field, $resp ) ) {
                unset( $resp[ $field ] );
            }
        }

        $data = [];

        foreach ( $resp as $field => $value ) {
            if ( is_array( $value ) ) {
                $data = array_merge( $data, $value );
            } else {
                $data[ $field ] = $value;
            }
        }

        return $data;
    }
}

<?php

namespace Ndpvp\Hook\Action\Type;

class Mailer {

    public function __construct() {
        add_action( 'phpmailer_init', [ $this, 'configure_smtp' ] );
    }

    public function configure_smtp( $phpmailer ) {
        $active = get_option( 'ndpv_smtp' );
        if ( $active ) {
            $smtp = get_option( 'ndpv_smtp_' . $active );
            if ( $active == 'other' ) {
                $phpmailer->Host = $smtp['host'];
                $phpmailer->Port = $smtp['port'];
                $phpmailer->Username = $smtp['user'];
                $phpmailer->Password = $smtp['pass'];
                $phpmailer->SMTPAuth = $smtp['auth'];
                if ( $smtp['secure'] ) {
                    $phpmailer->SMTPSecure = $smtp['secure'];
                }
                $phpmailer->IsSMTP();
            }
            /* else if ( $smtp == 'sendinblue' ) {
                //https://account.sendinblue.com/advanced/api
                $phpmailer->Host = 'smtp-relay.sendinblue.com'; // for example, smtp.mailtrap.io
                $phpmailer->Port = 587; // set the appropriate port: 465, 2525, etc.
                $phpmailer->Username = '<EMAIL>'; // your SMTP username
                $phpmailer->Password = 'rUVEs9F802HxdztY'; // your SMTP password
                $phpmailer->SMTPAuth = true;
                $phpmailer->SMTPSecure = 'tls'; // preferable but optional
                $phpmailer->IsSMTP();
            } elseif ($smtp == 'mailtrap') {
                //https://mailtrap.io/inboxes/1866478/messages
                $phpmailer->Host = 'smtp.mailtrap.io';
                $phpmailer->Port = 587;
                $phpmailer->Username = '495107477a48ec';
                $phpmailer->Password = '594deb0b5d2a93';
                $phpmailer->SMTPAuth = true;
                $phpmailer->SMTPSecure = 'tls';
                $phpmailer->IsSMTP();
            } elseif ($smtp == 'mailgun') {
                //https://app.mailgun.com/app/sending/domains/sandbox49d7bf028caf48f9b4204fba5d4bf329.mailgun.org
                $phpmailer->Host = 'smtp.mailgun.org';
                $phpmailer->Port = 587;
                $phpmailer->Username = '<EMAIL>';
                $phpmailer->Password = '**************************************************';
                $phpmailer->SMTPAuth = true;
                $phpmailer->SMTPSecure = 'tls';
                $phpmailer->IsSMTP();
            } */
        }
    }
}

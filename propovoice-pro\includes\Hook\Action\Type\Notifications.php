<?php

namespace Ndpvp\Hook\Action\Type;

class Notifications {

    public function __construct() {
        add_action( 'rest_api_init', [ $this, 'notification_routes' ] );
        add_action( 'wp_before_admin_bar_render', [ $this, 'add_notification_count_to_admin_bar' ] );
        add_action( 'admin_head', [ $this, 'add_custom_css' ] );
    }


    public function notification_routes() {
        register_rest_route(
            'ndpvp/v1',
            '/notifications/(?P<id>\d+)/mark-as-read' . ndpv()->plain_route(),
            [
                'methods' => 'GET',
                'callback' => [ $this, 'mark_as_read' ],
                'permission_callback' => '__return_true',
            ]
        );

        register_rest_route(
            'ndpvp/v1',
            '/notifications/users/(?P<id>\d+)/mark-all-as-read' . ndpv()->plain_route(),
            [
                'methods' => 'GET',
                'callback' => [ $this, 'mark_all_as_read' ],
                'permission_callback' => '__return_true',
            ]
        );

        register_rest_route(
            'ndpvp/v1', '/notifications/users/(?P<id>\d+)' . ndpv()->plain_route(), [
				'methods' => 'GET',
				'callback' => [ $this, 'get_user_notifications' ],
				'permission_callback' => '__return_true',
			]
        );

        register_rest_route(
            'ndpvp/v1',
            '/notifications/users/(?P<id>\d+)/count-new' . ndpv()->plain_route(),
            [
                'methods' => 'GET',
                'callback' => [ $this, 'count_new' ],
                'permission_callback' => '__return_true',
            ]
        );
        register_rest_route(
            'ndpvp/v1',
            '/notifications/users/(?P<id>\d+)/mark-as-old' . ndpv()->plain_route(),
            [
                'methods' => 'GET',
                'callback' => [ $this, 'mark_as_old' ],
                'permission_callback' => '__return_true',
            ]
        );

        register_rest_route(
            'ndpvp/v1',
            '/notifications/users/(?P<id>\d+)/preferences' . ndpv()->plain_route(),
            [
                'methods' => 'GET',
                'callback' => [ $this, 'get_user_preference' ],
                'permission_callback' => '__return_true',
            ]
        );

        register_rest_route(
            'ndpvp/v1',
            '/notifications/users/(?P<id>\d+)/preferences',
            [
                'methods' => 'POST',
                'callback' => [ $this, 'insert_or_update_user_preference' ],
                'permission_callback' => '__return_true',
            ]
        );

        register_rest_route(
            'ndpvp/v1',
            '/notifications/users/(?P<id>\d+)/fetch_delay' . ndpv()->plain_route(),
            [
                'methods' => 'GET',
                'callback' => [ $this, 'get_fetch_delay' ],
                'permission_callback' => '__return_true',
            ]
        );

        register_rest_route(
            'ndpvp/v1',
            '/notifications/users/(?P<id>\d+)/fetch_delay' . ndpv()->plain_route(),
            [
                'methods' => 'POST',
                'callback' => [ $this, 'set_fetch_delay' ],
                'permission_callback' => '__return_true',
            ]
        );
    }

    function add_notification_count_to_admin_bar() {
        global $wp_admin_bar;

        $current_user_id = get_current_user_id();
        $site_url = home_url();
        $count = 0;

        // Request to get notification count
        $response = wp_remote_get( "$site_url/wp-json/ndpvp/v1/notifications/users/$current_user_id/count-new" );

        $branding = esc_html__( 'Propovoice', 'propovoice-pro' );

        if ( ! is_wp_error( $response ) ) {
            $body = wp_remote_retrieve_body( $response );
            $count = json_decode( $body );
        }

        // Convert $count to a string
        $count_string = is_numeric($count) ? (string) $count : "0";

        // Add a notification menu item with the count
        $wp_admin_bar->add_menu(
            [
				'id'     => 'notif-count',
				'parent' => 'top-secondary',
				'title'  => $branding . ' <span class="ab-icon"></span><span class="ab-label">' . esc_html( $count_string ) . '</span>',
				'href'   => esc_url( "$site_url/workspace/" ),
			]
        );
	}

    public function add_custom_css() {
        // Custom css for admin bar notification count
        echo '<style type="text/css">
    #wpadminbar #wp-admin-bar-notif-count .ab-label {
    display: inline-block;
    vertical-align: middle;
    box-sizing: border-box;
	margin: 1px 0 -1px 2px;
	padding: 0 5px;
	min-width: 18px;
	height: 18px;
	border-radius: 9px;
	background-color: #ff267f;
	color: #fff;
	font-size: 11px;
    font-weight: bold;
	line-height: 1.6;
	text-align: center;
    z-index: 26;
    }
    </style>';
    }

    public function get_user_notifications( \WP_Rest_Request $req ) {
        global $wpdb;
        $param = $req->get_params();
        $user_id = isset( $param['id'] ) ? absint( $param['id'] ) : '';
        $filter = isset( $param['filter'] ) ? $param['filter'] : '';

        $activities_table = $wpdb->prefix . 'ndpv_activities';
        $notifications_table = $wpdb->prefix . 'ndpv_notifications';

        $where = $wpdb->prepare(
            "WHERE $notifications_table.notification_type = 'app'
                AND $notifications_table.receiver_id = %d", $user_id
        );

        if ( $filter == 'unseen' ) {
            $where .= " AND $notifications_table.is_seen=0";
        }

        $query = "
            SELECT $notifications_table.ID as notification_id, $activities_table.ID as activity_id, $notifications_table.*, $activities_table.*
            FROM $notifications_table
            LEFT JOIN $activities_table on $notifications_table.activity_id = $activities_table.id
           $where 
            ORDER BY $notifications_table.ID DESC
            LIMIT 50
            ";
        $results = $wpdb->get_results( $query );
        wp_send_json( $results );
    }

    public function count_new( \WP_Rest_Request $req ) {
        global $wpdb;
        $param = $req->get_params();
        $user_id = isset( $param['id'] ) ? absint( $param['id'] ) : '';
        $table_name = $wpdb->prefix . 'ndpv_notifications';
        $count = $wpdb->get_var(
            $wpdb->prepare(
                "SELECT COUNT(*) FROM $table_name
                WHERE notification_type = 'app'
                    AND receiver_id = %d
                    AND is_new=1", $user_id
            )
        );
        wp_send_json( $count );
    }

    public function mark_as_read( \WP_Rest_Request $req ) {
        global $wpdb;
        $param = $req->get_params();
        $notification_id = isset( $param['id'] ) ? absint( $param['id'] ) : 0;
        $table_name = $wpdb->prefix . 'ndpv_notifications';
        $data = [
            'is_seen' => 1,
        ];

        $where = [
            'ID' => $notification_id,
        ];

        $updated = $wpdb->update( $table_name, $data, $where );

        if ( $updated !== false ) {
            wp_send_json( 'Data updated successfully.' );
        } else {
            wp_send_json( 'Failed to update data.' );
        }
    }

    public function mark_all_as_read( \WP_Rest_Request $req ) {
        global $wpdb;
        $param = $req->get_params();
        $user_id = isset( $param['id'] ) ? absint( $param['id'] ) : 0;
        $table_name = $wpdb->prefix . 'ndpv_notifications';
        $data = [
            'is_seen' => 1,
        ];

        $where = [
            'receiver_id' => $user_id,
        ];

        $updated = $wpdb->update( $table_name, $data, $where );

        if ( $updated !== false ) {
            wp_send_json( 'Data updated successfully.' );
        } else {
            wp_send_json( 'Failed to update data.' );
        }
    }

    public function mark_as_old( \WP_Rest_Request $req ) {
        global $wpdb;
        $param = $req->get_params();
        $user_id = isset( $param['id'] ) ? absint( $param['id'] ) : 0;
        $table_name = $wpdb->prefix . 'ndpv_notifications';
        $data = [
            'is_new' => 0,
        ];

        $where = [
            'receiver_id' => $user_id,
        ];

        $updated = $wpdb->update( $table_name, $data, $where );

        if ( $updated !== false ) {
            wp_send_json( 'Data updated successfully.' );
        } else {
            wp_send_json( 'Failed to update data.' );
        }
    }

    public function get_user_preference( \WP_Rest_Request $req ) {
        global $wpdb;
        $param = $req->get_params();
        $user_id = isset( $param['id'] ) ? absint( $param['id'] ) : 0;
        $notification_type = isset( $param['notification_type'] ) ? $param['notification_type'] : '';

        $actions_table = $wpdb->prefix . 'ndpv_actions';
        $user_notification_preferences_table = $wpdb->prefix . 'ndpv_user_notification_preferences';

        $result = $wpdb->get_results(
            $wpdb->prepare(
                "SELECT *
            FROM $user_notification_preferences_table
            LEFT JOIN $actions_table on $user_notification_preferences_table.action_id = $actions_table.id
                WHERE notification_type = %s
                    AND user_id = %d
            ORDER BY $actions_table.ID
            ", $notification_type, $user_id
            )
        );

        wp_send_json( $result );
    }


    public function insert_or_update_user_preference( \WP_Rest_Request $req ) {
        global $wpdb;

        $user_notification_preferences_table = $wpdb->prefix . 'ndpv_user_notification_preferences';
        $param = $req->get_params();
        $user_id = isset( $param['id'] ) ? absint( $param['id'] ) : 0;
        $notification_type = isset( $param['notification_type'] ) ? $param['notification_type'] : '';
        $is_enable = isset( $param['is_enable'] ) ? absint( $param['is_enable'] ) : 0;
        $action_id = isset( $param['action_id'] ) ? absint( $param['action_id'] ) : 0;

        $actions = $this->get_all_actions();
        $actions_count = count( $actions );
        $user_preferences = $this->get_user_preferences( $user_id, $notification_type );
        $user_preferences_count = count( $user_preferences );

        $data = [
            'is_enabled' => $is_enable,
        ];

        $where = [
            'user_id' => $user_id,
            'notification_type' => $notification_type,
        ];

        if ( ! empty( $action_id ) ) {
            $where['action_id'] = $action_id;
        }

        ! empty( $user_preferences ) && $wpdb->update( $user_notification_preferences_table, $data, $where );

        if ( $user_preferences_count < $actions_count ) {
            foreach ( $actions as $action ) {
				// Skip if action_id already inserted
				$user_preferences_action_ids = array_column( $user_preferences, 'action_id' );
				if ( in_array( $action->ID, $user_preferences_action_ids, true ) ) {
					continue;
				}

				$wpdb->insert( $user_notification_preferences_table, array_merge( $data, $where, [ 'action_id' => $action->ID ] ) );
            }
        }

        wp_send_json( $is_enable );
    }

    public function get_user_preferences( int $user_id, string $notification_type ) {
        global $wpdb;
        $user_notification_preferences_table = $wpdb->prefix . 'ndpv_user_notification_preferences';

        $query = $wpdb->prepare(
            "
            SELECT *
            FROM $user_notification_preferences_table as p
            WHERE p.user_id = %d
                AND p.notification_type = %s
            ", $user_id, $notification_type
        );
        return $wpdb->get_results( $query );
    }

    public function get_all_actions() {
        global $wpdb;
        $actions_table = $wpdb->prefix . 'ndpv_actions';

        $query = "
            SELECT *
            FROM $actions_table
            ";
        return $wpdb->get_results( $query );
    }

	public function get_fetch_delay( \WP_Rest_Request $req ) {
        $param = $req->get_params();
        $user_id = isset( $param['id'] ) ? absint( $param['id'] ) : 0;
        $delay = get_user_meta( $user_id, 'ndpv_notif_fetch_delay', true );
        wp_send_json( $delay );
	}

	public function set_fetch_delay( \WP_Rest_Request $req ) {
        $param = $req->get_params();
        $user_id = isset( $param['id'] ) ? absint( $param['id'] ) : 0;
        $delay_sec = isset( $param['delay'] ) ? absint( $param['delay'] ) : 0;
        $delay = update_user_meta( $user_id, 'ndpv_notif_fetch_delay', $delay_sec );
        wp_send_json( $delay );
	}
}

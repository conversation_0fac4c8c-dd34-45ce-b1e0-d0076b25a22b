<?php

namespace Ndpvp\Hook\Action\Type;

use Ndpvp\Api\Type\WooCommerce\WCOrder;
use Ndpvp\Api\Type\Export\Csv as ExportCsv;
use Ndpvp\Api\Type\Import\Csv as ImportCsv;
use Ndpvp\Api\Type\PaymentMethod\PayPal;

class RestRoute {

    private $wc_order;
    private $export_csv;
    private $import_csv;

    public function __construct() {
        $this->export_csv = new ExportCsv();
        $this->import_csv = new ImportCsv();
        $this->wc_order = new WCOrder();
        add_action( 'rest_api_init', [ $this, 'create_route' ] );
    }

    public function create_route() {
        // Woocommerce
        register_rest_route(
            'ndpvp/v1', '/wc-order/(?P<id>\d+)', [
				'methods' => 'GET',
				'callback' => [ $this->wc_order, 'wc_order' ],
				'permission_callback' => function () {
					return true;
				},
			]
        );

        // Export
        register_rest_route(
            'ndpvp/v1', '/export/csv', [
				'methods' => 'POST',
				'callback' => [ $this->export_csv, 'export' ],
				'permission_callback' => function () {
					return true;
				},
			]
        );

        // Import
        register_rest_route(
            'ndpvp/v1', '/import/csv', [
				'methods' => 'POST',
				'callback' => [ $this->import_csv, 'import' ],
				'permission_callback' => function () {
					return true;
				},
			]
        );

        //Paypal Subscription
        register_rest_route(
            'ndpvp/v1',
            '/invoices/(?P<id>\d+)/paypal-subscriptions',
            [
                'methods' => 'GET',
                'callback' => [ new PayPal(), 'create_subs' ],
                'permission_callback' => function () {
                    return true;
                },
            ]
        );

        //Save Paypal Subscription ID
        register_rest_route(
            'ndpvp/v1',
            '/invoices/(?P<invoice_id>\d+)/paypal-subscriptions/(?P<subs_id>[a-zA-Z0-9-]+)',
            [
                'methods' => 'GET',
                'callback' => [ new PayPal(), 'save_subs_id' ],
                'permission_callback' => function () {
                    return true;
                },
            ]
        );

        // PayPal WebHook listener
        register_rest_route(
            'ndpvp/v1', '/paypal-webhooks', [
				'methods' => 'POST',
				'callback' => [ new PayPal(), 'listen_webhook' ],
				'permission_callback' => function () {
					return true;
				},
			]
        );
    }
}

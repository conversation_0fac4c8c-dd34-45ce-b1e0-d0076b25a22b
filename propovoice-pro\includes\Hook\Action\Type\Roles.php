<?php

namespace Ndpvp\Hook\Action\Type;

use Ndpv\Hook\Type\Action\Role;

class Roles {


    public $roles;

    public $ndpv_caps;

    public function __construct() {
        $this->ndpv_caps = Role::$ndpv_caps;
        $this->roles = [
            [
                'key' => 'ndpv_admin',
                'label' => esc_html__( 'Propovoice Admin', 'propovoice-pro' ),
                'caps' => [
                    'ndpv_core' => true,
                    'ndpv_dashboard' => true,
                    'ndpv_lead' => true,
                    'ndpv_deal' => true,
                    'ndpv_estimate' => true,
                    'ndpv_invoice' => true,
                    'ndpv_package' => true,
                    'ndpv_order' => true,
                    'ndpv_request' => true,
                    'ndpv_client' => true,
                    'ndpv_project' => true,
                    'ndpv_action' => true,
                    'ndpv_business' => true,
                    'ndpv_contact' => true,
                    'ndpv_email' => true,
                    'ndpv_file' => true,
                    'ndpv_form' => true,
                    'ndvp_media' => true,
                    'ndpv_note' => true,
                    'ndpv_org' => true,
                    'ndpv_payment' => true,
                    'ndpv_person' => true,
                    'ndpv_setting' => true,
                    'ndpv_task' => true,
                    'ndpv_taxonomy' => true,
                    'ndpv_webhook' => true,
                    'ndpv_workspace' => true,
                    'ndpv_workflow' => true,
                ],
            ],
            [
                'key' => 'ndpv_manager',
                'label' => esc_html__( 'Propovoice Manager', 'propovoice-pro' ),
                'caps' => [
                    'ndpv_core' => true,
                    'ndpv_dashboard' => true,
                    'ndpv_lead' => true,
                    'ndpv_deal' => true,
                    'ndpv_estimate' => true,
                    'ndpv_invoice' => true,
                    'ndpv_package' => true,
                    'ndpv_order' => true,
                    'ndpv_request' => true,
                    'ndpv_client' => true,
                    'ndpv_project' => true,
                    'ndpv_action' => true,
                    'ndpv_business' => true,
                    'ndpv_contact' => true,
                    'ndpv_email' => true,
                    'ndpv_file' => true,
                    'ndpv_form' => true,
                    'ndvp_media' => true,
                    'ndpv_note' => true,
                    'ndpv_org' => true,
                    'ndpv_payment' => true,
                    'ndpv_person' => true,
                    'ndpv_setting' => true,
                    'ndpv_task' => true,
                    'ndpv_taxonomy' => true,
                    'ndpv_webhook' => true,
                    'ndpv_workspace' => true,
                    'ndpv_workflow' => true,
                ],
            ],
            [
                'key' => 'ndpv_staff',
                'label' => esc_html__( 'Propovoice Staff', 'propovoice-pro' ),
                'caps' => [
                    'ndpv_core' => true,
                    'ndpv_dashboard' => true,
                    'ndpv_project' => true,
                    'ndpv_package' => true,
                    'ndpv_order' => true,
                    'ndpv_request' => true,
                    'ndpv_action' => true,
                    'ndpv_business' => true,
                    'ndpv_email' => true,
                    'ndpv_file' => true,
                    'ndpv_form' => true,
                    'ndvp_media' => true,
                    'ndpv_note' => true,
                    'ndpv_org' => true,
                    'ndpv_payment' => true,
                    'ndpv_person' => true,
                    'ndpv_task' => true,
                    'ndpv_taxonomy' => true,
                    'ndpv_webhook' => true,
                    'ndpv_workspace' => true,
                    'ndpv_setting' => true,
                    'ndpv_workflow' => true,
                ],
            ],
            [
                'key' => 'ndpv_client_role', //added role text because already added caps
                'label' => esc_html__( 'Propovoice Client', 'propovoice-pro' ),
                'caps' => [
                    'ndpv_core' => true,
                    'ndpv_workspace' => true,
                    'ndpv_dashboard' => true,
                    'ndpv_estimate' => true,
                    'ndpv_invoice' => true,
                    'ndpv_package' => true,
                    'ndpv_order' => true,
                    'ndpv_request' => true,
                    'ndpv_project' => true,
                    'ndpv_task' => true,
                    'ndpv_taxonomy' => true,
                    'ndpv_note' => true,
                    'ndpv_file' => true,
                    'ndvp_media' => true,
                    'ndpv_setting' => true,
                    'ndpv_workflow' => true,
                ],
            ],
        ];

        add_action( 'init', [ $this, 'register' ] );
        add_action( 'init', [ $this, 'add_package_caps_for_existing_users' ] );
        add_action( 'init', [ $this, 'add_workflow_cap_for_existing_users' ] );
        // add_action("init", [$this, "remove"]);
    }

    public function register() {
        $remove_role = get_option( 'ndpv_removed_role', false );
        if ( ! $remove_role ) {
            $old_roles = [
                'ndpv_staff',
                'ndpv_project_manager',
                'ndpv_billing_manager',
                'ndpv_crm_manager',
            ];
            foreach ( $old_roles as $val ) {
                remove_role( $val );
            }

            update_option( 'ndpv_removed_role', true );
        }

        $subs_caps = get_role( 'subscriber' )->capabilities;
        foreach ( $this->roles as $role ) {
            $ndpv_caps_with_false = array_fill_keys(
                array_keys( $this->ndpv_caps ),
                false
            );
            $caps = array_merge( $ndpv_caps_with_false, $role['caps'] );
            add_role( $role['key'], $role['label'], array_merge( $subs_caps, $caps ) );
        }
    }

    public function remove() {
        foreach ( $this->roles as $role ) {
            remove_role( $role['key'] );
        }
    }

	public function add_package_caps_for_existing_users() {
		$roles = [ 'administrator', 'ndpv_admin', 'ndpv_manager', 'ndpv_staff', 'ndpv_client_role' ];
		$package_caps = [ 'ndpv_package', 'ndpv_order', 'ndpv_request' ];

		foreach ( $roles as $role ) {
			$selected_role = get_role( $role );
			foreach ( $package_caps as $pc ) {
				$selected_role->add_cap( $pc );
			}
		}
    }

	public function add_workflow_cap_for_existing_users() {
		$roles = [ 'administrator', 'ndpv_admin', 'ndpv_manager', 'ndpv_staff', 'ndpv_client_role' ];

		foreach ( $roles as $role ) {
			$selected_role = get_role( $role );
			$selected_role->add_cap( 'ndpv_workflow' );
		}
    }
}

<?php

namespace Ndpvp\Hook\Action\Type;

use Ndpv\Helpers\Fns;

class WC {

    public function __construct() {
        add_action(
            'woocommerce_order_status_completed', [
				$this,
				'order_complete',
			]
        );
    }

    public function order_complete( int $order_id ) {
        $invoice_id = get_post_meta( $order_id, '_ndpv_invoice_id', true );

        if ( $invoice_id ) {
            update_post_meta( $invoice_id, 'status', 'paid' );
        }
        if ( ! is_admin() && $invoice_id ) {
            wp_safe_redirect(
                sprintf(
                    '%s?id=%s&token=%s',
                    Fns::client_page_url( 'invoice' ),
                    $invoice_id,
                    get_post_meta( $invoice_id, 'token', true )
                )
            );
            exit();
        }
    }
}

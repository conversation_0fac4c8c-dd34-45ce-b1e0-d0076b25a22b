<?php

namespace Ndpvp\Hook\Action\Type;

use Ndpvp\Hook\Action\Type\Activities;

class Webhook {

    public function __construct() {
        add_action( 'ndpvp_webhook', [ $this, 'webhook' ], 10, 2 );
    }

    function webhook( $type, $param ) {

        //notification
        $activities = new Activities();
        $activities->manage_activity( $type, $param );

        //modify params
        if ( $type === 'lead_add' || $type === 'lead_edit' || $type === 'lead_level_change' ) {
            $level_id = isset( $param['level_id'] ) ? absint( $param['level_id'] ) : null;
            if ( $level_id ) {
                $level = get_term_by( 'id', $level_id, 'ndpv_lead_level' );
                $param['level_id'] = [
                    'id' => $level->term_id,
                    'label' => $level->name,
                ];
            }

            $img = isset( $param['img'] ) ? absint( $param['img'] ) : null;
            if ( $img ) {
                $img_src = wp_get_attachment_image_url( $img, 'full' );
                $param['img'] = [
                    'id' => $img,
                    'src' => $img_src,
                ];
            }
        } elseif ( $type === 'deal_add' || $type === 'deal_edit' || $type === 'deal_stage_change' ) {
            $stage_id = isset( $param['stage_id'] ) ? absint( $param['stage_id'] ) : null;
            if ( $stage_id ) {
                $stage = get_term_by( 'id', $stage_id, 'ndpv_deal_stage' );
                $param['stage_id'] = [
                    'id' => $stage->term_id,
                    'label' => $stage->name,
                ];
            }
        } elseif ( $type === 'project_add' || $type === 'project_edit' || $type === 'project_status_change' ) {
            $status_id = isset( $param['status_id'] ) ? absint( $param['status_id'] ) : null;
            if ( $status_id ) {
                $status = get_term_by( 'id', $status_id, 'ndpv_project_status' );
                $param['status_id'] = [
                    'id' => $status->term_id,
                    'label' => $status->name,
                ];
            }
        } elseif ( $type === 'task_add' || $type === 'task_edit' ) {
            $status_id = isset( $param['status_id'] ) ? absint( $param['status_id'] ) : null;
            if ( $status_id ) {
                $status = get_term_by( 'id', $status_id, 'ndpv_task_status' );
                $param['status_id'] = [
                    'id' => $status->term_id,
                    'label' => $status->name,
                ];
            }
        }

        if (
            $type === 'lead_add' ||
            $type === 'lead_edit' ||
            $type === 'deal_add' ||
            $type === 'deal_edit'
        ) {
            $tags = isset( $param['tags'] ) ? array_map( 'absint', $param['tags'] ) : null;
            if ( $tags ) {
                $tag_list = [];
                foreach ( $tags as $tag ) {
                    $level = get_term_by( 'id', $tag, 'ndpv_tag' );
                    $tag_list[] = [
                        'id' => $level->term_id,
                        'label' => $level->name,
                    ];
                }
                $param['tags'] = $tag_list;
            }
        }

        $args = [
            'post_type' => 'ndpv_webhook',
            'post_status' => 'publish',
            'posts_per_page' => -1,
            'fields' => 'ids',
        ];

        $query = new \WP_Query( $args );
        foreach ( $query->posts as $id ) {
            $active = get_post_meta( $id, 'active', true );
            if ( $active ) {
                $actions = get_post_meta( $id, 'actions', true );
                if ( $actions && in_array( $type, $actions ) ) {
                    $url = get_post_meta( $id, 'url', true );
                    if ( $url ) {
                        $param = apply_filters( 'ndpvp_webhook_' . $type, $param );
                        wp_remote_post(
                            $url, [
								'timeout' => 0.01,
								'body' => $param,
								'blocking'  => false,
								'sslverify'   => false,
							]
                        );
                    }
                }
            }
        }
        wp_reset_postdata();
    }
}

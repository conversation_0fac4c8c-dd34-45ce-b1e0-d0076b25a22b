<?php

namespace Ndpvp\Hook\Filter\Type;

class Menu {

    public function __construct() {
        add_filter( 'ndpv_sidebar_menu', [ $this, 'sidebar_menu' ] );
    }

    public function sidebar_menu( $data ) {
        if ( ! ndpvp()->wage() ) {
            return $data;
        }

        $new = [
            [
                'id' => 'task',
                'label' => __( 'Task', 'propovoice-pro' ),
                'capability' => 'ndpv_task',
            ],
        ];

        $extended = array_merge(
            array_slice( $data, 0, 4 ),
            $new,
            array_slice( $data, 4 )
        );

        if ( method_exists( ndpv(), 'is_active_module' ) && ndpv()->is_active_module() ) {
            $service = [
                [
                    'id' => 'package',
                    'label' => esc_html__( 'Package', 'propovoice-pro' ),
                    'capability' => 'ndpv_package',
				],
                [
                    'id' => 'order',
                    'label' => esc_html__( 'Order', 'propovoice-pro' ),
                    'capability' => 'ndpv_order',
				],
                [
                    'id' => 'request',
                    'label' => esc_html__( 'Request', 'propovoice-pro' ),
                    'capability' => 'ndpv_request',
                ],
            ];

            $extended = array_merge(
                array_slice( $extended, 0, 7 ),
                $service,
                array_slice( $extended, 7 )
            );
        }

        return apply_filters( 'ndpvp_sidebar_menu', $extended );
    }
}

<?php

namespace Ndpvp\Hook\Filter\Type;

class WC {

    public function __construct() {
        add_filter(
            'woocommerce_get_order_item_totals',
            [ $this, 'remove_subtotal_from_orders_total_lines' ],
            10,
            1
        );
        add_filter(
            'woocommerce_get_checkout_order_received_url',
            [ $this, 'change_order_received_url_for_ndpv' ],
            10,
            2
        );
    }

    function remove_subtotal_from_orders_total_lines( $totals ) {
        // error_log(wc_get_notices("error"));
        if ( isset( $_GET['pay_to'] ) && $_GET['pay_to'] === 'ndpv' ) {
            unset( $totals['cart_subtotal'] );
        }
        return $totals;
    }

    function change_order_received_url_for_ndpv( $order_received_url, $that ) {
        if ( isset( $_GET['pay_to'] ) && $_GET['pay_to'] === 'ndpv' ) {
            return $order_received_url . '&pay_to=ndpv';
        }
        return $order_received_url;
    }
}

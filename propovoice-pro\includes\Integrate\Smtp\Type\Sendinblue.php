<?php

namespace Ndpvp\Integrate\Smtp\Type;

class Sendinblue {


    public function __construct() {
        add_action( 'rest_api_init', [ $this, 'rest_routes' ] );
    }

    public function rest_routes() {
        register_rest_route(
            'ndpv/v1', '/intg-smtp/sendinblue', [
				'methods' => 'GET',
				'callback' => [ $this, 'get' ],
				'permission_callback' => [ $this, 'get_per' ],
			]
        );

        register_rest_route(
            'ndpv/v1', '/intg-smtp/sendinblue', [
				'methods' => 'POST',
				'callback' => [ $this, 'create' ],
				'permission_callback' => [ $this, 'create_permission' ],
			]
        );
    }

    public function get( $req ) {
        $param = $req->get_params();
        $reg_errors = new \WP_Error();

        $tab = isset( $param['tab'] ) ? sanitize_text_field( $param['tab'] ) : null;

        $forms = [];

        $args = [
            'post_type' => 'wpcf7_contact_form',
            'posts_per_page' => -1,
        ];

        $cf7_query = new \WP_Query( $args );

        if ( ! $cf7_query->have_posts() ) {
            return $forms;
        } else {
            while ( $cf7_query->have_posts() ) {
                $cf7_query->the_post();

                global $post;

                $cf7 = \WPCF7_ContactSmtp::get_instance( $post->ID );

                $form = [
                    'id'     => $post->ID,
                    'title'  => $post->post_title,
                    'fields' => [],
                ];

                foreach ( $cf7->collect_mail_tags() as $tag ) {
                    $form['fields'][] = [
                        'id'    => $tag,
                        'label' => "[{$tag}]",
                    ];
                }

                $forms[] = $form;
            }
        }
        wp_send_json_success( $forms );
    }

    public function create( $req ) {
        $param = $req->get_params();
        $reg_errors = new \WP_Error();

        $tab = isset( $param['tab'] ) ? sanitize_text_field( $param['tab'] ) : null;

        if ( empty( $tab ) ) {
            $reg_errors->add( 'field', esc_html__( 'Tab is missing', 'propovoice-pro' ) );
        }

        if ( $reg_errors->get_error_messages() ) {
            wp_send_json_error( $reg_errors->get_error_messages() );
        } else {
            $data = [];

            wp_send_json_success();
        }
    }

    public function get_per() {
        return true;
    }

    public function create_permission() {
        return current_user_can( 'publish_posts' );
    }
}

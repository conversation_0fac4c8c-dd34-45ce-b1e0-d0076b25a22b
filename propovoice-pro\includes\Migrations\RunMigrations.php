<?php

namespace Ndpvp\Migrations;

use Ndpvp\Migrations\Tables\SchemaMigrations;
use Ndpvp\Migrations\Tables\Actions;
use Ndpvp\Migrations\Tables\UserNotificationPreferences;
use Ndpvp\Migrations\Tables\Activities;
use Ndpvp\Migrations\Tables\Notifications;

class RunMigrations {

    private $last_migration_version;
    public function __construct() {
        add_action( 'plugins_loaded', [ $this, 'get_schema_migration' ] );
    }

    //
    //
    // To create a new migration
    // 1. Go to "run" method
    // 2. Specify a version and table name inside "migratio_to_table" method
    // 3. Go to table folder and specified table file
    // 4. call the parent::version_migration method inside "run_migration" method with version, table_name and method name where you will write your SQL command
    //
    //

    public function run() {
        $this->migration_to_table( 2.0, Actions::class );
        $this->migration_to_table( 3.0, UserNotificationPreferences::class );
        $this->migration_to_table( 4.0, Activities::class );
        $this->migration_to_table( 5.0, Notifications::class );
        $this->migration_to_table( 6.0, Activities::class );
        $this->migration_to_table( 7.0, Actions::class );
        $this->migration_to_table( 8.0, Actions::class );
        $this->migration_to_table( 9.0, Activities::class );
        $this->migration_to_table( 10.0, Notifications::class );
        $this->migration_to_table( 11.0, UserNotificationPreferences::class );
        $this->migration_to_table( 12.0, Actions::class );
        $this->migration_to_table( 13.0, Actions::class );
        $this->migration_to_table( 14.0, UserNotificationPreferences::class );
        $this->migration_to_table( 15.0, UserNotificationPreferences::class );
    }

    public function get_schema_migration() {
        global $wpdb;
        $table_name = $wpdb->prefix . 'ndpv_schema_migrations';

        $table_exists = $this->is_schema_migrations_table_exists( $table_name );
        // Check if the table exists
        if ( $table_exists ) {
            $this->last_migration_version = $this->get_last_migration_version(
                $table_name
            );
            $this->run();
        } else {
            new SchemaMigrations( 1.0 );
        }
    }

    public function migration_to_table( $version, $class_name ) {
        if ( $version > $this->last_migration_version ) {
            new $class_name( $version );
            $this->last_migration_version = $version;
        }
    }

    public function is_schema_migrations_table_exists( $table_name ) {
        global $wpdb;
        $sql = "SHOW TABLES LIKE '$table_name'";
        return $wpdb->get_var( $sql );
    }

    public function get_last_migration_version( $table_name ) {
        global $wpdb;
        $last_migration = $wpdb->get_row(
            "SELECT * FROM $table_name ORDER BY id DESC LIMIT 1"
        );
        return (float) $last_migration->version;
    }
}

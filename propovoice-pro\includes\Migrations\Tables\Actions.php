<?php

namespace Ndpvp\Migrations\Tables;

use Ndpvp\Migrations\Table;
use Ndpvp\Migrations\Helpers;

class Actions extends Table {

    private $table_name = 'ndpv_actions';

    public function run_migration( $current_version, $helpers ) {
        parent::version_migration(
            2.0, $current_version, $this->table_name, [
				$this,
				'create_actions_table',
			]
        );
        parent::version_migration(
            7.0, $current_version, $this->table_name, [
				$this,
				'remove_some_action_7_0',
			]
        );
        parent::version_migration(
            8.0, $current_version, $this->table_name, [
				$this,
				'add_project_add_8_0',
			]
        );
        parent::version_migration(
            12.0, $current_version, $this->table_name, [
				$this,
				'add_package_assign_12_0',
			]
        );
        parent::version_migration(
            13.0, $current_version, $this->table_name, [
				$this,
				'order_and_request_add_13_0',
			]
        );
    }

    public function create_actions_table( $helpers ) {
        $helpers->create_table(
            $this->table_name,
            'ID BIGINT(20) UNSIGNED NOT NULL AUTO_INCREMENT,
            slug VARCHAR(255) NOT NULL,
            label VARCHAR(255),
            PRIMARY KEY (ID)'
        );
        $this->insert_action_data();
    }

    public function insert_action_data() {
        //lead_add, lead_edit, lead_del, lead_asign
        global $wpdb;
        $data = [
            [
                'slug' => 'lead_add',
                'label' => esc_html__( 'Lead add', 'propovoice-pro' ),
            ],
            [
                'slug' => 'lead_assign',
                'label' => esc_html__( 'Lead assign', 'propovoice-pro' ),
            ],
            [
                'slug' => 'lead_level_change',
                'label' => esc_html__( 'Lead level change', 'propovoice-pro' ),
            ],
            [
                'slug' => 'deal_add',
                'label' => esc_html__( 'Deal add', 'propovoice-pro' ),
            ],
            [
                'slug' => 'deal_assign',
                'label' => esc_html__( 'Deal assign', 'propovoice-pro' ),
            ],
            [
                'slug' => 'deal_stage_change',
                'label' => esc_html__( 'Deal Stage change', 'propovoice-pro' ),
            ],
            [
                'slug' => 'est_add',
                'label' => esc_html__( 'Estimate add', 'propovoice-pro' ),
            ],
            [
                'slug' => 'est_edit',
                'label' => esc_html__( 'Estimate edit', 'propovoice-pro' ),
            ],

            [
                'slug' => 'staff_add',
                'label' => esc_html__( 'Team member added', 'propovoice-pro' ),
            ],
            [
                'slug' => 'task_add',
                'label' => esc_html__( 'Create Task', 'propovoice-pro' ),
            ],

            [
                'slug' => 'task_edit',
                'label' => esc_html__( 'Edit Task', 'propovoice-pro' ),
            ],
            [
                'slug' => 'task_assign',
                'label' => esc_html__( 'Assign Task', 'propovoice-pro' ),
            ],
            [
                'slug' => 'task_status_change',
                'label' => esc_html__( 'Change task status', 'propovoice-pro' ),
            ],
            [
                'slug' => 'lead_to_deal',
                'label' => esc_html__( 'Move to deal pipeline', 'propovoice-pro' ),
            ],
            [
                'slug' => 'project_discussion_add',
                'label' => esc_html__( 'Discussion in Project', 'propovoice-pro' ),
            ],
            [
                'slug' => 'file_add',
                'label' => esc_html__( 'Add file', 'propovoice-pro' ),
            ],
            [
                'slug' => 'receive_message',
                'label' => esc_html__( 'Receive Message', 'propovoice-pro' ),
            ],

            [
                'slug' => 'assignee_create_est',
                'label' => esc_html__( 'Assignee create estimate', 'propovoice-pro' ),
            ],

            [
                'slug' => 'est_accept',
                'label' => esc_html__( 'Accept estimate', 'propovoice-pro' ),
            ],
            [
                'slug' => 'est_reject',
                'label' => esc_html__( 'Reject estimate', 'propovoice-pro' ),
            ],
            [
                'slug' => 'inv_add',
                'label' => esc_html__( 'Create invoice', 'propovoice-pro' ),
            ],
            [
                'slug' => 'assignee_create_inv',
                'label' => esc_html__( 'Assignee Create invoice', 'propovoice-pro' ),
            ],
            [
                'slug' => 'inv_edit',
                'label' => esc_html__( 'Edit invoice', 'propovoice-pro' ),
            ],
            [
                'slug' => 'inv_paid',
                'label' => esc_html__( 'Paid invoice', 'propovoice-pro' ),
            ],

            [
                'slug' => 'inv_paid_req',
                'label' => esc_html__( 'Paid request for invoice', 'propovoice-pro' ),
            ],
            [
                'slug' => 'inv_reject',
                'label' => esc_html__( 'Reject invoice', 'propovoice-pro' ),
            ],
        ];
        foreach ( $data as $row ) {
            $wpdb->insert( $wpdb->prefix . $this->table_name, $row );
        }
    }

    public function remove_some_action_7_0( $helpers ) {

        global $wpdb;
        // Define the list of slugs you want to delete
        $slugs_to_delete = [
            'task_edit',
            'receive_message',
            'assignee_create_est',
            'assignee_create_inv',
            'inv_reject',
        ];

        // Format the WHERE clause based on the list of slugs
        $slug_condition = [];
        foreach ( $slugs_to_delete as $slug ) {
            $slug_condition[] = $wpdb->prepare( 'slug=%s', $slug );
        }

        // Join the WHERE clauses with "OR"
        $where_clause = implode( ' OR ', $slug_condition );

        // Prepare and execute the DELETE query
        // $wpdb->delete($wpdb->prefix . $this->table_name, $where_clause);
        $table_name = $wpdb->prefix . $this->table_name;
        $wpdb->query( "DELETE FROM $table_name WHERE $where_clause" );
        if ( $wpdb->rows_affected > 0 ) {
            error_log( 'row affected!' );
        } else {
            error_log( 'No rows were deleted or an error occurred.' );
        }
    }

    public function add_project_add_8_0( $helpers ) {
        global $wpdb;
        $data = [
            [
                'slug' => 'project_add',
                'label' => esc_html__( 'Project add', 'propovoice-pro' ),
            ],
		];

        foreach ( $data as $row ) {
            $wpdb->insert( $wpdb->prefix . $this->table_name, $row );
        }
    }

	public function add_package_assign_12_0( $helpers ) {
        global $wpdb;
        $data = [
            [
                'slug' => 'package_assign',
                'label' => esc_html__( 'Package assign ', 'propovoice-pro' ),
            ],
		];

        foreach ( $data as $row ) {
            $wpdb->insert( $wpdb->prefix . $this->table_name, $row );
		}
	}

	public function order_and_request_add_13_0( $helpers ) {
        global $wpdb;
        $data = [
            [
                'slug' => 'order_add',
                'label' => esc_html__( 'Create order', 'propovoice-pro' ),
            ],
            [
                'slug' => 'request_add',
                'label' => esc_html__( 'Create request', 'propovoice-pro' ),
            ],
		];

        foreach ( $data as $row ) {
            $wpdb->insert( $wpdb->prefix . $this->table_name, $row );
		}
	}
}

<?php

namespace Ndpvp\Migrations\Tables;

use Ndpvp\Migrations\Table;
use Ndpvp\Migrations\Helpers;

class Activities extends Table {

    private $table_name = 'ndpv_activities';

    public function run_migration( $current_version, $helpers ) {
        parent::version_migration(
            4.0, $current_version, $this->table_name, [
				$this,
				'create_activities_table',
			]
        );
        parent::version_migration(
            6.0, $current_version, $this->table_name, [
				$this,
				'alter_action_id_6_0',
			]
        );
        parent::version_migration(
            9.0, $current_version, $this->table_name, [
				$this,
				'regenerate_table_for_multisite_9_0',
			]
        );
    }

    public function create_activities_table( $helpers ) {
        global $wpdb;
		// Check if the current engine is InnoDB
		$current_engine = $wpdb->get_var( "SELECT ENGINE FROM information_schema.TABLES WHERE TABLE_SCHEMA = '{$wpdb->dbname}'" );
		$sql = 'ID BIGINT(20) UNSIGNED NOT NULL AUTO_INCREMENT,
            created_at DATETIME,
            message TEXT,
            post_id BIGINT(20) UNSIGNED,
            action_id BIGINT(20) UNSIGNED,
            created_by BIGINT(20) UNSIGNED,
            PRIMARY KEY (ID)';

		if ( $current_engine === 'InnoDB' ) {
			$sql .= ",
            FOREIGN KEY (post_id) REFERENCES {$wpdb->prefix}posts (ID) ON DELETE CASCADE,
            FOREIGN KEY (action_id) REFERENCES {$wpdb->prefix}ndpv_actions (ID) ON DELETE CASCADE,
            FOREIGN KEY (created_by) REFERENCES {$wpdb->base_prefix}users (ID) ON DELETE CASCADE";
		}

        $helpers->create_table(
            $this->table_name,
            $sql
        );
    }
    public function alter_action_id_6_0() {

        global $wpdb;
        $table_name = $wpdb->prefix . $this->table_name;
        // Get foreign key constraints on the table
        $sql = "SELECT CONSTRAINT_NAME
        FROM INFORMATION_SCHEMA.REFERENTIAL_CONSTRAINTS
        WHERE TABLE_NAME = '$table_name'
            AND REFERENCED_TABLE_NAME = '{$wpdb->prefix}ndpv_actions';";
        $constraint_name = $wpdb->get_var( $sql );

        if ( $constraint_name ) {
            // Drop the existing foreign key constraint
            $wpdb->query( "ALTER TABLE $table_name DROP FOREIGN KEY $constraint_name" );

            // Add a new foreign key constraint with ON DELETE CASCADE
            $wpdb->query( "ALTER TABLE $table_name ADD CONSTRAINT action_id FOREIGN KEY (action_id) REFERENCES {$wpdb->prefix}ndpv_actions (ID) ON DELETE CASCADE" );
        }
    }
	public function regenerate_table_for_multisite_9_0( $helpers ) {
		global $wpdb;
		$table_name = $wpdb->prefix . $this->table_name;

		// SQL query to check if the table exists
		$table_exists = $wpdb->get_var( "SHOW TABLES LIKE '$table_name'" ) === $table_name;

		// Check the result
		if ( ! $table_exists ) {
			$this->create_activities_table( $helpers );
		}
	}
}

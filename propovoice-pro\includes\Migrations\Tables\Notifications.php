<?php

namespace Ndpvp\Migrations\Tables;

use Ndpvp\Migrations\Table;
use Ndpvp\Migrations\Helpers;

class Notifications extends Table {

    private $table_name = 'ndpv_notifications';

    public function run_migration( $current_version, $helpers ) {
        parent::version_migration(
            5.0, $current_version, $this->table_name, [
				$this,
				'create_notifications_table',
			]
        );
        parent::version_migration(
            10.0, $current_version, $this->table_name, [
				$this,
				'regenerate_table_for_multisite_10_0',
			]
        );
    }

    public function create_notifications_table( $helpers ) {
        global $wpdb;

		$current_engine = $wpdb->get_var( "SELECT ENGINE FROM information_schema.TABLES WHERE TABLE_SCHEMA = '{$wpdb->dbname}'" );

		$sql = 'ID BIGINT(20) UNSIGNED NOT NULL AUTO_INCREMENT,
            created_at DATETIME NOT NULL,
            activity_id BIGINT(20) UNSIGNED NOT NULL ,
            receiver_id BIGINT(20) UNSIGNED NOT NULL ,
            is_seen TINYINT(1) NOT NULL DEFAULT 0,
            is_mail_sent TINYINT(1) NOT NULL DEFAULT 0,
            is_new TINYINT(1) NOT NULL DEFAULT 1,
            notification_type VARCHAR(255),
            mail_sent_at DATETIME,
            seen_at DATETIME,
            PRIMARY KEY (ID)';

		if ( $current_engine === 'InnoDB' ) {
			$sql .= ",
            FOREIGN KEY (activity_id) REFERENCES {$wpdb->prefix}ndpv_activities (ID) ON DELETE CASCADE,
            FOREIGN KEY (receiver_id) REFERENCES {$wpdb->base_prefix}users (ID) ON DELETE CASCADE";
        }

        $helpers->create_table(
            $this->table_name,
            $sql
        );
    }
	public function regenerate_table_for_multisite_10_0( $helpers ) {
		global $wpdb;
		$table_name = $wpdb->prefix . $this->table_name;

		// SQL query to check if the table exists
		$table_exists = $wpdb->get_var( "SHOW TABLES LIKE '$table_name'" ) === $table_name;

		// Check the result
		if ( ! $table_exists ) {
			$this->create_notifications_table( $helpers );
		}
	}
}

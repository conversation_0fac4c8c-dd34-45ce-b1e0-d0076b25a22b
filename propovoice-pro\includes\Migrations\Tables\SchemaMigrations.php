<?php
namespace Ndpvp\Migrations\Tables;

use Ndpvp\Migrations\Table;
use Ndpvp\Migrations\Helpers;

class SchemaMigrations extends Table {

    private $table_name = 'ndpv_schema_migrations';
    public function run_migration( $current_version, $helpers ) {
        parent::version_migration(
            1.0, $current_version, $this->table_name, [
				$this,
				'create_schema_migrations_table',
			]
        );
    }

    public function create_schema_migrations_table( $helpers ) {
        $helpers->create_table(
            $this->table_name,
            'ID BIGINT(20) UNSIGNED NOT NULL AUTO_INCREMENT,
            version VARCHAR(255) NOT NULL,
            table_name VARCHAR(255) NOT NULL,
            applied_at DATETIME,
            PRIMARY KEY (ID)'
        );
    }
}

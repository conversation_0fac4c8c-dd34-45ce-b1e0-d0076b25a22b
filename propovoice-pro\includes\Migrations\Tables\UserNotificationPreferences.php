<?php

namespace Ndpvp\Migrations\Tables;

use Ndpvp\Migrations\Table;
use Ndpvp\Migrations\Helpers;

class UserNotificationPreferences extends Table {

    private $table_name = 'ndpv_user_notification_preferences';

    public function run_migration( $current_version, $helpers ) {
        parent::version_migration(
            3.0, $current_version, $this->table_name, [
				$this,
				'create_user_notification_preferences_table',
			]
        );
        parent::version_migration(
            11.0, $current_version, $this->table_name, [
				$this,
				'regenerate_table_for_multisite_11_0',
			]
        );
        parent::version_migration(
            14.0, $current_version, $this->table_name, [
				$this,
				'generate_package_notif_for_existing_users_14_0',
			]
        );
        parent::version_migration(
            15.0, $current_version, $this->table_name, [
				$this,
				'fix_generate_package_notif_for_existing_users_15_0',
			]
        );
    }

    public function create_user_notification_preferences_table( $helpers ) {
        global $wpdb;

		$current_engine = $wpdb->get_var( "SELECT ENGINE FROM information_schema.TABLES WHERE TABLE_SCHEMA = '{$wpdb->dbname}'" );

		$sql = 'ID BIGINT(20) UNSIGNED NOT NULL AUTO_INCREMENT,
            user_id BIGINT(20) UNSIGNED,
            action_id BIGINT(20) UNSIGNED,
            notification_type VARCHAR(255),
            is_enabled TINYINT(1),
            PRIMARY KEY (ID)';

		if ( $current_engine === 'InnoDB' ) {
			$sql .= ",
            FOREIGN KEY (user_id) REFERENCES {$wpdb->base_prefix}users (ID) ON DELETE CASCADE,
            FOREIGN KEY (action_id) REFERENCES {$wpdb->prefix}ndpv_actions (ID) ON DELETE CASCADE";
        }

        $helpers->create_table(
            $this->table_name,
            $sql
        );
    }

	public function regenerate_table_for_multisite_11_0( $helpers ) {
		global $wpdb;
		$table_name = $wpdb->prefix . $this->table_name;

		// SQL query to check if the table exists
		$table_exists = $wpdb->get_var( "SHOW TABLES LIKE '$table_name'" ) === $table_name;

		// Check the result
		if ( ! $table_exists ) {
			$this->create_user_notification_preferences_table( $helpers );
		}
	}

	public function generate_package_notif_for_existing_users_14_0( $helpers ) {
	}

	public function fix_generate_package_notif_for_existing_users_15_0( $helpers ) {
		$users = get_users();
		$notification_types = [ 'mail', 'app' ];
		$action_slugs = [ 'package_assign', 'order_add', 'request_add' ];
		$action_ids = $this->get_action_ids( $action_slugs );

		foreach ( $action_ids as $action_id ) {
			foreach ( $notification_types as $notification_type ) {
				foreach ( $users as $user ) {
					$is_exists = $this->is_preferences_exists( $user->ID, $action_id, $notification_type );
					if ( ! $is_exists ) {
						$this->insert_new_preferences( $user->ID, $action_id, $notification_type );
					}
				}
			}
		}
	}

	public function is_preferences_exists( $user_id, $action_id, $notification_type ) {
		global $wpdb;
		$table_name = $wpdb->prefix . $this->table_name;
		$sql = $wpdb->prepare(
            "SELECT COUNT(*)
      FROM $table_name
      WHERE user_id=%d
            AND action_id=%d
            AND notification_type=%s", $user_id, $action_id, $notification_type
        );
		$row_count = $wpdb->get_var( $sql );

		if ( $row_count > 0 ) {
			return true;
		}
		return false;
	}

	public function get_action_ids( $action_slugs ) {
		global $wpdb;
		$table_name = $wpdb->prefix . 'ndpv_actions';
		$action_ids = [];

		foreach ( $action_slugs as $action_slug ) {
			$sql = $wpdb->prepare(
                "SELECT ID
        FROM $table_name
        WHERE slug=%s", $action_slug
            );
			$action_id = $wpdb->get_var( $sql );
			$action_ids[] = $action_id;
		}

		return $action_ids;
	}

	public function insert_new_preferences( $user_id, $action_id, $notification_type ) {
		global $wpdb;
		$row = [
            'user_id' => $user_id,
            'action_id' => $action_id,
            'notification_type' => $notification_type,
            'is_enabled' => true,
		];

		$wpdb->insert( $wpdb->prefix . $this->table_name, $row );
	}
}

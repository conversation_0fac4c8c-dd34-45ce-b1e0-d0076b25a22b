<?php

namespace Ndpvp\Models;

class ActivitiesModel {

    public function insert_activity( $args ) {
        global $wpdb;
        $table_name = $wpdb->prefix . 'ndpv_activities';
        $actions_table = $wpdb->prefix . 'ndpv_actions';
        $action_id = $wpdb->get_var(
            $wpdb->prepare(
                "SELECT ID FROM $actions_table WHERE slug = %s",
                $args['action_slug']
            )
        );
        unset( $args['action_slug'] );
        $data = array_merge(
            $args, [
				'action_id' => (int) $action_id,
				'created_at' => date( 'Y-m-d H:i:s' ),
			]
        );

        $result = $wpdb->insert( $table_name, $data );

        if ( $result === false ) {
            // Error occurred
        } else {
            // Insert successful, retrieve the inserted rows
            $insert_id = $wpdb->insert_id; // Get the ID of the last inserted row

            return $wpdb->get_row(
                $wpdb->prepare(
                    "SELECT * FROM $table_name WHERE id = %d",
                    $insert_id
                )
            );
        }
    }
}

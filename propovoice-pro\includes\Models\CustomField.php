<?php
namespace Ndpvp\Models;

use Ndpv\Helpers\Fns;

class CustomField {

    public function get( $mod = '' ) {
        global $wpdb;
        $tbl_name = $wpdb->prefix . 'ndpv_custom_fields';
        $results = $wpdb->get_results(
            $wpdb->prepare( "SELECT * FROM {$tbl_name} WHERE `mod` = %s ORDER BY `order`", $mod )
        );

        if ( $results ) {
            $modify_results = [];
            foreach ( $results as $result ) {
                $data = [];

                $data['id'] = $result->id;
                $data['slug'] = $result->slug ? $result->slug : Fns::slugify_key( $result->label );
                $data['type'] = $result->type;
                $data['label'] = $result->label;
                $data['desc'] = $result->desc;
                //'[]' check for old version value
                $data['options'] = ( $result->options === '[]' ) ? [] : maybe_unserialize( $result->options );
                $data['value'] = maybe_unserialize( $result->value );
                $modify_results[] = $data;
            }
            return $modify_results;
        }

        return [];
    }
}

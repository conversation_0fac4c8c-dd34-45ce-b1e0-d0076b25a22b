<?php

namespace Ndpvp\Models;

use Ndpv\Models\Business;
use Ndpv\Api\Type\Setting;

class NotificationsModel {

    public function __construct() {
        // add_action("ndpv_save_and_send_notification", [
        //     $this,
        //     "save_and_send_notification",
        // ]);
    }

    public function manage_notification( $activity, $extra_args ) {
        $users = $this->get_target_user( $activity );

        foreach ( $users as $user ) {
            $notification_types = [ 'mail', 'app' ];
            foreach ( $notification_types as $type ) {
                $args = [
                    'activity_id' => $activity->ID,
                    'receiver_id' => $user->ID,
                    'notification_type' => $type,
                    'created_at' => date( 'Y-m-d H:i:s' ),
                ];
                $is_enabled = $this->is_notification_enabled( $user->ID, $activity->action_id, $type );

                $is_enabled &&
                    $this->create_notification( $args );

                if ( $type == 'mail' ) {
                    $is_enabled && $this->send_mail(
                        $user,
                        $activity,
                        $extra_args
                    );
                }
            }
        }
    }

    public function get_target_user( $activity ) {
		$assignee_ids = [];
		$client_user_id = [];

		if ( isset( $activity->post_id ) ) {
			$assignee_ids = get_post_meta(
                $activity->post_id,
                '_ndpv_allowed_users',
                true
			);
		}

        $admin_and_manager = get_users(
            [
				'role__in' => [ 'administrator', 'ndpv_admin', 'ndpv_manager' ],
				'login__not_in' => [ 'ndpv_whatsapp' ],
				'fields' => 'ID',
			]
        );

        // get client id when action slug of activity is "project_discussion_add"
        // 1. get persion_id and org_id meta value by post_id
        // 2. get user by ndpv_client_id meta key, persion_id+org_id meta value

		if ( isset( $activity->post_id ) ) {
			$client_user_id = $this->get_client_user_id( $activity->post_id, $activity->action_id );
		}

        $user_ids = array_unique( array_merge( (array) $assignee_ids, $admin_and_manager, (array) $client_user_id ) );
        $user_ids = array_diff( $user_ids, [ (int) $activity->created_by ] );

        if ( empty( $user_ids ) ) {
            return [];
        }

        $users = get_users(
            [
				'include' => $user_ids,
			]
        );

        return $users;
    }

    private function get_client_user_id( $post_id, $action_id ) {
        global $wpdb;

        // Check permitted action slugs for client
        $action_slugs = [ 'est_add', 'project_add', 'project_discussion_add' ];
        $action_slug = $this->get_action_slug( $action_id );
        if ( ! in_array( $action_slug, $action_slugs ) ) {
            return false;
        }

        // get person id
        $post_type = get_post_type( $post_id );
        $post_type_person_meta_key = [
            'ndpv_estinv' => 'to',
            'ndpv_project' => 'person_id',
        ];
        $person_post_id = get_post_meta( $post_id, $post_type_person_meta_key[ $post_type ], true );

        // Check person is client and has portal access
        $is_client = get_post_meta( $person_post_id, 'is_client', true );
        $is_portal_access = get_post_meta( $person_post_id, 'client_portal', true );

        // get user id
        if ( $is_client && $is_portal_access ) {
            $query = $wpdb->prepare(
                "SELECT user_id FROM $wpdb->usermeta WHERE meta_key = %s AND meta_value = %s",
                'ndpv_client_id',
                $person_post_id
            );

            // Return user id
            return $wpdb->get_var( $query );
        }
        return false;
    }

    public function get_action_slug( $id ) {
        global $wpdb;
        $actions_table = $wpdb->prefix . 'ndpv_actions';
        $query = $wpdb->prepare(
            "SELECT slug FROM $actions_table WHERE id = %d",
            $id
        );

        // Return action slug
        return $wpdb->get_var( $query );
    }

    public function create_notification( $data ) {
        global $wpdb;
        $table_name = $wpdb->prefix . 'ndpv_notifications';

        $wpdb->insert( $table_name, $data );
    }

    public function is_notification_enabled( $user_id, $action_id, $notification_type ) {
        global $wpdb;

        $actions_table = $wpdb->prefix . 'ndpv_actions';
        $user_notification_preferences_table = $wpdb->prefix . 'ndpv_user_notification_preferences';

        $is_enabled = $wpdb->get_var(
            $wpdb->prepare(
                "SELECT p.is_enabled as is_enabled
                FROM $user_notification_preferences_table as p
                LEFT JOIN $actions_table as a ON p.action_id = a.id
                WHERE p.user_id = %d
                    AND p.notification_type = %s
                    AND a.ID = %d", $user_id, $notification_type, $action_id
            )
        );

        return ! is_null( $is_enabled ) ? intval( $is_enabled ) : true;
    }

    public function dsend_mail( $to, $subject, $activity ) {
        $message = $activity->message;
        $site_name = get_bloginfo( 'name' );
        $site_email = get_option( 'admin_email' );
        $headers = [
            'Content-Type: text/html; charset=UTF-8',
            "From: $site_name <$site_email>",
        ];
        $result = wp_mail( $to, $subject, $message, $headers );

        /* if ($result) {
            echo "Email sent successfully.";
        } else {
            echo "Failed to send the email.";
        } */
    }

    /**
     *  Password mail
     *
     * @package NDPV Project
     * @since 1.0
     */
    public function send_mail( $user, $activity, $extra_args ) {
        //sent mail

        $data = [];
        $name = $user->display_name;
        $notification = $extra_args['title'];
        $notification_link = $activity->message;

        // Get email template
        $action = $this->get_action( $activity->action_id );
        $email_template = 'email_' . $action->slug . '_notif';
        $setting = new Setting();
        $data = $setting->get_email_template( $setting->tabs['system_email_template'], $email_template, $data );

        $mail_subject = $data['subject'];
        $msg = nl2br( $data['msg'] ?? '' );

        $business = new Business();
        $business_info = $business->info();
        $org_name = $business_info['name'];
        $org_email = $business_info['email'];
        // $permalink = Fns::client_page_url("workspace");

        $subject = self::pass_email_variable(
            $mail_subject, [
				'org_name' => $org_name,
				'notification' => $notification,
			]
        );

        // Remove html tags from subject
        $subject = strip_tags( $subject );

        $template = ndpv()->render( 'email/password', [], true );
        $template = str_replace( '{msg}', $msg, $template );
        $body = self::pass_email_variable(
            $template, [
				'org_name' => $org_name,
				'notification_link' => $notification_link,
				'name' => $name,
			]
        );

        $headers = [ 'Content-Type: text/html; charset=UTF-8' ];
        $headers[] = 'From: ' . $org_name . ' <' . $org_email . '>';
        return wp_mail( $user->user_email, $subject, $body, $headers, [] );
    }

    public function get_action( $id ) {
        global $wpdb;
        $actions_table = $wpdb->prefix . 'ndpv_actions';

        $query = $wpdb->prepare(
            "SELECT *
            FROM $actions_table as a
            WHERE a.id = %d", $id
        );
        return $wpdb->get_row( $query );
    }

    private static function pass_email_variable( $string, $array = [] ) {
        $org_name = isset( $array['org_name'] ) ? $array['org_name'] : '';
        $notification = isset( $array['notification'] ) ? $array['notification'] : '';
        $name = isset( $array['name'] ) ? $array['name'] : '';
        $notification_link = isset( $array['notification_link'] ) ? $array['notification_link'] : '';
        return str_replace(
            [
                '{org_name}',
                '{notification}',
                '{name}',
                '{notification_link}',
            ],
            [
                $org_name,
                $notification,
                $name,
                $notification_link,
            ],
            $string ?? ''
        );
    }
}

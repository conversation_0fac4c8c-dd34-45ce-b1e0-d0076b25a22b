<?php

require_once __DIR__ . '/../vendor/autoload.php';

use Ndpvp\Setup\InstallCtrl;
use Ndpvp\Helpers\Constant;
use Ndpvp\Helpers\Fns;
use Ndpvp\Helpers\Preset;
use Ndpvp\Migrations\RunMigrations;
use Ndpvp\Traits\Singleton;

/**
 * Ndpvp Final Class
 *
 * Here Ndpvp means Nurency Digital Propovoice Pro
 *
 * @since 1.0.0
 *
 * @class Ndpvp The class that holds the entire Ndpvp plugin
 */
final class Ndpvp {

    use Singleton;

    /**
     * Constructor for the Ndpvp class
     *
     * Sets up all the appropriate hooks and actions
     * within our plugin.
     */
    public function __construct() {
        new Constant();
        new RunMigrations();
        new InstallCtrl();
        $this->init_hooks();
    }

    private function init_hooks() {
        add_action( 'plugins_loaded', [ $this, 'on_plugins_loaded' ], -1 );
        add_action( 'init', [ $this, 'init' ], 1 );

        add_action( 'admin_notices', [ $this, 'license_active_alert' ] );
    }

    public function init() {
        do_action( 'ndpvp_before_init' );

        $this->localization_setup();

        new Ndpvp\Api\ApiCtrl();
        new Ndpvp\Hook\HookCtrl();
        new Ndpvp\Cron\CronCtrl();
        new Ndpvp\Integrate\IntegrateCtrl();

        do_action( 'ndpvp_init' );
    }

    public function on_plugins_loaded() {
        do_action( 'ndpvp_loaded' );
    }

    /**
     * Initialize plugin for localization
     *
     * @since 1.0.0
     *
     * @uses load_plugin_textdomain()
     *
     * @return void
     */
    public function localization_setup() {
        load_plugin_textdomain( 'propovoice-pro', false, plugin_basename( dirname( NDPVP_FILE ) ) . '/languages' );
    }

    /**
     * Get the plugin path.
     *
     * @since 1.0.0
     *
     * @return string
     */
    public function plugin_path() {
        return untrailingslashit( plugin_dir_path( NDPVP_FILE ) );
    }

    /**
     * Plugin version
     *
     * @since 1.0.0
     *
     * @var string
     */
    public function version() {
        return NDPVP_VERSION;
    }

    /**
     * Get the template path.
     *
     * @return string
     */
    public function get_template_path() {
        return apply_filters( 'ndpvp_template_path', 'ndpvp/templates/' );
    }

    /**
     * Get the template partial path.
     *
     * @since 1.0.0
     *
     * @return string
     */
    public function get_partial_path( $path = null, $args = [] ) {
        Fns::get_template_part( 'partial/' . $path, $args );
    }

    /**
     * Get asset uri depend on file
     *
     * @since 1.0.0
     *
     * @param $file
     *
     * @return string
     */
    public function get_asset_uri( $file ) {
        $file = ltrim( $file, '/' );

        return trailingslashit( NDPVP_URL . '/asset' ) . $file;
    }

    /**
     * Render file from view folder
     *
     * @since 1.0.0
     *
     * @param $file
     *
     * @return void
     */
    public function render( $view_name, $args = [], $return = false ) {
        $path = str_replace( '.', '/', $view_name );
        $view_path = NDPVP_PATH . '/views/' . $path . '.php';
        if ( ! file_exists( $view_path ) ) {
            return;
        }

        if ( $args ) {
            extract( $args );
        }

        if ( $return ) {
            ob_start();
            include $view_path;

            return ob_get_clean();
        }
        include $view_path;
    }

    /**
     * Get options field by args
     *
     * @since 1.0.0
     *
     * @return void
     */
    public function get_options() {
        $option_field = func_get_args()[0];
        $result = get_option( $option_field );
        $func_args = func_get_args();
        array_shift( $func_args );

        foreach ( $func_args as $arg ) {
            if ( is_array( $arg ) ) {
                if ( ! empty( $result[ $arg[0] ] ) ) {
                    $result = $result[ $arg[0] ];
                } else {
                    $result = $arg[1];
                }
            } elseif ( ! empty( $result[ $arg ] ) ) {
                    $result = $result[ $arg ];
			} else {
				$result = null;
            }
        }
        return $result;
    }

    public function wage() {
        $settings = ndpv()->get_options( 'ndpv_wage' );
        $valid = isset( $settings['status'] ) && ! empty( $settings['status'] ) && $settings['status'] === 'valid';
        if ( $this->wagex() ) {
            $valid = false;
        }
        return apply_filters( 'ndpv_wage_check', $valid );
    }

    public function wagex() {
        $settings = ndpv()->get_options( 'ndpv_wage' );
        $exp = false;
        if ( isset( $settings['expires'] ) && $settings['expires'] != 'lifetime' ) {
            // Convert the given date string to a DateTime object
            $given_date = new DateTime( $settings['expires'] );
            // Get the current date and time as a DateTime object
            $current_date = new DateTime();
            // Compare the two dates
            if ( $given_date < $current_date ) {
                $exp = true;
            }
        }
        return $exp;
    }

    public function is_active_module() {
        $settings = ndpv()->get_options( 'ndpv_wage' );
        $is_active = true;
        if ( isset( $settings['for'] ) && (
            $settings['for'] == '1' ||
            $settings['for'] == '3'
            // $settings["for"] == '14' || //tier 1
            // $settings["for"] == '15' // tier 2
        ) ) {
            $is_active = false;
        }
        return $is_active;
    }

    public function license_active_alert() {
        if ( $this->wage() ) {
            return;
        }

        $class = 'notice notice-error';
        $message = '';
        $url = '';
        $button_text = '';

        $message = esc_html__( 'Please active Propovoice Pro to get automatic updates, premium support and unlimited access to all the features of Propovoice Pro.', 'propovoice-pro' );
        $url = admin_url( 'admin.php?page=ndpv#/setting/license' );
        $button_text = esc_html__( 'Activate License Now', 'propovoice-pro' );

        if ( ! isset( $_GET['action'] ) || sanitize_text_field( $_GET['action'] ) !== 'install-plugin' ) {
            printf(
                '
            <div class="%1$s">
                <p>%2$s</p>
                <p><a class="button button-primary" href="%3$s">%4$s</a></p>
            </div>',
                $class,
                $message,
                $url,
                $button_text
            );
        }
    }

    /**
     * Get default value by args
     *
     * @since 1.0.0
     *
     * @return void
     */
    public function get_default() {
        $data = new Preset();
        $result = $data->default();
        $func_args = func_get_args();

        foreach ( $func_args as $arg ) {
            if ( is_array( $arg ) ) {
                if ( ! empty( $result[ $arg[0] ] ) ) {
                    $result = $result[ $arg[0] ];
                } else {
                    $result = $arg[1];
                }
            } elseif ( ! empty( $result[ $arg ] ) ) {
                    $result = $result[ $arg ];
			} else {
				$result = null;
            }
        }
        return $result;
    }
}

/**
 * Initialize the main plugin.
 *
 * @since 1.0.0
 *
 * @return Ndpvp
 */
function ndpvp() {
    return Ndpvp::getInstance();
}
ndpvp(); // Run Ndpvp Plugin

<?php
namespace Ndpvp\Setup;

use Ndpvp\Setup\Type\DB;

class InstallCtrl {

    public function __construct() {
        add_action( 'admin_init', [ $this, 'insert_data' ] );
    }

    public function insert_data() {
        $version = get_option( 'ndpvp_version', '1.0.0' );
        if ( version_compare( $version, NDPVP_VERSION, '<' ) ) {
            update_option( 'ndpvp_version', NDPVP_VERSION );
        }

        if ( version_compare( $version, '1.3.2.1', '<' ) ) {
            //
        }
        new DB();
    }
}

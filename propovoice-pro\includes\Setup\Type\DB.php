<?php

namespace Ndpvp\Setup\Type;

class DB {

    public function __construct() {
        $this->create_table();
    }

    public function create_table() {

        $db_version = '1.0.3';
        $tbl_prefix = 'ndpv_';

        $version = get_option( 'ndpvp_db_version', '0.1.0' );
        if ( version_compare( $version, $db_version, '<' ) ) {
            update_option( 'ndpvp_db_version', $db_version );
        }

        if ( version_compare( $version, '1.0.3', '<' ) ) {
            global $wpdb;
            $tbl_name = $wpdb->prefix . $tbl_prefix . 'custom_fields';

            $installer_sql = "CREATE TABLE $tbl_name (
                id mediumint(9) NOT NULL AUTO_INCREMENT,
                `mod` tinytext DEFAULT '' NOT NULL,
                label varchar(100) DEFAULT '' NOT NULL,
                slug varchar(110) DEFAULT '' NOT NULL,
                `desc` text DEFAULT '' NOT NULL,
                type tinytext DEFAULT '' NOT NULL,
                options text DEFAULT '' NOT NULL,
                `value` text NULL,
                `order` tinytext DEFAULT '' NOT NULL,
                `is_required` tinytext NULL,
                status tinytext DEFAULT '' NOT NULL,
                time datetime DEFAULT '0000-00-00 00:00:00' NOT NULL,
                PRIMARY KEY  (id)
            );";

            require_once ABSPATH . 'wp-admin/includes/upgrade.php';

            dbDelta( $installer_sql );
        }

        if ( version_compare( $version, '1.0.1', '<' ) ) {
            global $wpdb;
            $tbl_name = $wpdb->prefix . $tbl_prefix . 'messages';

            $installer_sql = "CREATE TABLE $tbl_name (
                id mediumint(9) NOT NULL AUTO_INCREMENT,
                sender_id int(11) NOT NULL,
                receiver_id int(11) NOT NULL,
                receiver_type mediumint(9) NOT NULL, /* 1 = client, 2 = team */
                `text` text NOT NULL DEFAULT '',
                attach_ids text DEFAULT '',
                module_id int(11),
                `source` tinytext NOT NULL DEFAULT '',
                status tinytext NOT NULL DEFAULT '',
                date datetime NOT NULL DEFAULT '0000-00-00 00:00:00',
                PRIMARY KEY (id),
                INDEX idx_sender (sender_id),
                INDEX idx_receiver (receiver_id),
                INDEX idx_receiver_type (receiver_type),
                INDEX idx_source (source(20))
            );";

            require_once ABSPATH . 'wp-admin/includes/upgrade.php';

            dbDelta( $installer_sql );
        }
    }
}

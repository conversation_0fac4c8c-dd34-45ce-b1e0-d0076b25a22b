<?php

/**
 * Singleton
 *
 * @since 1.1.0
 * @package NDPVP Project
 * <AUTHOR>
 */

namespace Ndpvp\Traits;

trait Singleton {

    /**
     * Store the singleton object.
     */
    private static $singleton = false;

    /**
     * Fetch an instance of the class.
     */
    public static function getInstance() {
        if ( self::$singleton === false ) {
            self::$singleton = new self();
        }

        return self::$singleton;
    }
}

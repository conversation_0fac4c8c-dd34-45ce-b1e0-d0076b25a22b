# Copyright (c) 2024 <PERSON><PERSON><PERSON> <PERSON>. All Rights Reserved.
msgid ""
msgstr ""
"Project-Id-Version: Propovoice Pro 1.7.1.4\n"
"Report-Msgid-Bugs-To: https://github.com/therakib7/propovoice-pro/issues\n"
"Last-Translator: <EMAIL>\n"
"Language-Team: LANGUAGE <<EMAIL>>\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: 8bit\n"
"POT-Creation-Date: 2024-05-15T06:02:00+00:00\n"
"PO-Revision-Date: YEAR-MO-DA HO:MI+ZONE\n"
"X-Generator: WP-CLI 2.10.0\n"
"X-Domain: propovoice-pro\n"

#. Plugin Name of the plugin
#: propovoice-pro.php
#: propovoice-pro.php:48
#: propovoice-pro.php:167
msgid "Propovoice Pro"
msgstr ""

#. Plugin URI of the plugin
#: propovoice-pro.php
msgid "https://wordpress.org/plugins/propovoice"
msgstr ""

#. Description of the plugin
#: propovoice-pro.php
msgid "This is a pro plugin of Propovoice."
msgstr ""

#. Author of the plugin
#: propovoice-pro.php
#: includes/Hook/Action/Type/Notifications.php:113
#: propovoice-pro.php:47
#: propovoice-pro.php:166
msgid "Propovoice"
msgstr ""

#. Author URI of the plugin
#: propovoice-pro.php
msgid "https://propovoice.com"
msgstr ""

#: includes/Api/Type/Action.php:37
msgid "Required field is missing"
msgstr ""

#: includes/Api/Type/CustomField.php:83
#: includes/Api/Type/CustomField.php:122
#: includes/Api/Type/CustomField.php:186
msgid "Module is missing"
msgstr ""

#: includes/Api/Type/CustomField.php:128
msgid "Date field already used"
msgstr ""

#: includes/Api/Type/CustomField.php:143
msgid "Field alaready exist"
msgstr ""

#: includes/Api/Type/Form.php:40
msgid "Form is missing"
msgstr ""

#: includes/Api/Type/License.php:62
#: includes/Api/Type/License.php:94
#: includes/Api/Type/License.php:133
msgid "An error occurred, please try again."
msgstr ""

#: includes/Api/Type/License.php:70
msgid "Your license key expired on %s."
msgstr ""

#: includes/Api/Type/License.php:78
msgid "Your license key has been disabled."
msgstr ""

#: includes/Api/Type/License.php:81
msgid "Invalid license."
msgstr ""

#: includes/Api/Type/License.php:85
msgid "Your license is not active for this URL."
msgstr ""

#: includes/Api/Type/License.php:88
msgid "This appears to be an invalid license key for Propovoice Pro."
msgstr ""

#: includes/Api/Type/License.php:91
msgid "Your license key has reached its activation limit."
msgstr ""

#: includes/Api/Type/License.php:111
msgid "License successfully activated"
msgstr ""

#: includes/Api/Type/License.php:140
msgid "License successfully deactivated"
msgstr ""

#: includes/Api/Type/Message.php:153
msgid "Me"
msgstr ""

#: includes/Api/Type/Message.php:155
msgid "Client"
msgstr ""

#: includes/Api/Type/Message.php:161
msgid "ago"
msgstr ""

#: includes/Api/Type/Message.php:218
#: includes/Api/Type/Message.php:279
#: includes/Api/Type/Setting.php:39
#: includes/Api/Type/Setting.php:81
#: includes/Integrate/Smtp/Type/Sendinblue.php:81
msgid "Tab is missing"
msgstr ""

#: includes/Api/Type/Message.php:225
msgid "Receiver type is missing"
msgstr ""

#: includes/Api/Type/Message.php:232
#: includes/Api/Type/Message.php:286
msgid "Message is missing"
msgstr ""

#: includes/Api/Type/PaymentProcess.php:113
msgid "Propovoice Package ID: "
msgstr ""

#: includes/Api/Type/PaymentProcess.php:113
msgid "Propovoice Invoice ID: "
msgstr ""

#: includes/Api/Type/Staff.php:185
msgid "Tab id is missing"
msgstr ""

#: includes/Api/Type/Staff.php:192
msgid "User is missing"
msgstr ""

#: includes/Api/Type/Subscription.php:225
#: includes/Api/Type/Subscription.php:320
msgid "Contact info is missing"
msgstr ""

#. translators: %s: Invoice ID
#: includes/Api/Type/WooCommerce/WCOrder.php:65
msgid "Total amount for invoice id #%s"
msgstr ""

#: includes/Cron/Type/Recurring.php:201
msgid "Invoice"
msgstr ""

#: includes/Cron/Type/Recurring.php:201
msgid "Estimate"
msgstr ""

#: includes/Hook/Action/Type/Activities.php:30
msgid "<b>Lead added</b> by <b> %s </b>"
msgstr ""

#: includes/Hook/Action/Type/Activities.php:34
msgid "<b>Lead level changed</b> by <b> %s </b>"
msgstr ""

#: includes/Hook/Action/Type/Activities.php:38
msgid "<b>Deal added</b> by <b> %s </b>"
msgstr ""

#: includes/Hook/Action/Type/Activities.php:43
msgid "<b>Deal stage changed</b> by <b> %s </b>"
msgstr ""

#: includes/Hook/Action/Type/Activities.php:48
msgid "<b>Lead moved to Deal</b> by <b> %s </b>"
msgstr ""

#: includes/Hook/Action/Type/Activities.php:53
msgid "<b>Task added</b> by <b> %s </b>"
msgstr ""

#: includes/Hook/Action/Type/Activities.php:66
msgid "<b>Task status changed</b> by <b> %s </b>"
msgstr ""

#: includes/Hook/Action/Type/Activities.php:73
msgid "<b>File added</b> by <b> %s </b>"
msgstr ""

#: includes/Hook/Action/Type/Activities.php:78
msgid "<b>Estimate added</b> by <b> %s </b>"
msgstr ""

#: includes/Hook/Action/Type/Activities.php:83
msgid "<b>Estimate updated</b> by <b> %s </b>"
msgstr ""

#: includes/Hook/Action/Type/Activities.php:87
msgid "<b>Estimate Accepted</b>"
msgstr ""

#: includes/Hook/Action/Type/Activities.php:91
msgid "<b>Estimate rejected</b>"
msgstr ""

#: includes/Hook/Action/Type/Activities.php:96
msgid "<b>Invoice added</b> by <b> %s </b>"
msgstr ""

#: includes/Hook/Action/Type/Activities.php:101
msgid "<b>Invoice updated</b> by <b> %s </b>"
msgstr ""

#: includes/Hook/Action/Type/Activities.php:105
msgid "<b>Invoice paid</b> by <b> %s </b>"
msgstr ""

#: includes/Hook/Action/Type/Activities.php:110
msgid "<b>Invoice Payment submitted</b> by <b> %s </b>"
msgstr ""

#: includes/Hook/Action/Type/Activities.php:115
msgid "<b>Invoice rejected</b> by <b> %s </b>"
msgstr ""

#: includes/Hook/Action/Type/Activities.php:120
msgid "<b>Project added</b> by <b> %s </b>"
msgstr ""

#: includes/Hook/Action/Type/Activities.php:125
msgid "<b>Project Discussion added</b> by <b> %s </b>"
msgstr ""

#: includes/Hook/Action/Type/Activities.php:130
msgid "<b>Team member added </b> by <b> %s </b>"
msgstr ""

#: includes/Hook/Action/Type/Activities.php:134
msgid "<b>Team member assigned to %1$s </b> by <b> %2$s </b>"
msgstr ""

#: includes/Hook/Action/Type/Activities.php:138
msgid "<b>Order added</b> by <b> %s </b>"
msgstr ""

#: includes/Hook/Action/Type/Activities.php:142
msgid "<b>Request added</b> by <b> %s </b>"
msgstr ""

#: includes/Hook/Action/Type/Roles.php:19
msgid "Propovoice Admin"
msgstr ""

#: includes/Hook/Action/Type/Roles.php:53
msgid "Propovoice Manager"
msgstr ""

#: includes/Hook/Action/Type/Roles.php:87
msgid "Propovoice Staff"
msgstr ""

#: includes/Hook/Action/Type/Roles.php:115
msgid "Propovoice Client"
msgstr ""

#: includes/Hook/Filter/Type/Menu.php:19
msgid "Task"
msgstr ""

#: includes/Hook/Filter/Type/Menu.php:34
msgid "Package"
msgstr ""

#: includes/Hook/Filter/Type/Menu.php:39
msgid "Order"
msgstr ""

#: includes/Hook/Filter/Type/Menu.php:44
msgid "Request"
msgstr ""

#: includes/Migrations/Tables/Actions.php:62
msgid "Lead add"
msgstr ""

#: includes/Migrations/Tables/Actions.php:66
msgid "Lead assign"
msgstr ""

#: includes/Migrations/Tables/Actions.php:70
msgid "Lead level change"
msgstr ""

#: includes/Migrations/Tables/Actions.php:74
msgid "Deal add"
msgstr ""

#: includes/Migrations/Tables/Actions.php:78
msgid "Deal assign"
msgstr ""

#: includes/Migrations/Tables/Actions.php:82
msgid "Deal Stage change"
msgstr ""

#: includes/Migrations/Tables/Actions.php:86
msgid "Estimate add"
msgstr ""

#: includes/Migrations/Tables/Actions.php:90
msgid "Estimate edit"
msgstr ""

#: includes/Migrations/Tables/Actions.php:95
msgid "Team member added"
msgstr ""

#: includes/Migrations/Tables/Actions.php:99
msgid "Create Task"
msgstr ""

#: includes/Migrations/Tables/Actions.php:104
msgid "Edit Task"
msgstr ""

#: includes/Migrations/Tables/Actions.php:108
msgid "Assign Task"
msgstr ""

#: includes/Migrations/Tables/Actions.php:112
msgid "Change task status"
msgstr ""

#: includes/Migrations/Tables/Actions.php:116
msgid "Move to deal pipeline"
msgstr ""

#: includes/Migrations/Tables/Actions.php:120
msgid "Discussion in Project"
msgstr ""

#: includes/Migrations/Tables/Actions.php:124
msgid "Add file"
msgstr ""

#: includes/Migrations/Tables/Actions.php:128
msgid "Receive Message"
msgstr ""

#: includes/Migrations/Tables/Actions.php:133
msgid "Assignee create estimate"
msgstr ""

#: includes/Migrations/Tables/Actions.php:138
msgid "Accept estimate"
msgstr ""

#: includes/Migrations/Tables/Actions.php:142
msgid "Reject estimate"
msgstr ""

#: includes/Migrations/Tables/Actions.php:146
msgid "Create invoice"
msgstr ""

#: includes/Migrations/Tables/Actions.php:150
msgid "Assignee Create invoice"
msgstr ""

#: includes/Migrations/Tables/Actions.php:154
msgid "Edit invoice"
msgstr ""

#: includes/Migrations/Tables/Actions.php:158
msgid "Paid invoice"
msgstr ""

#: includes/Migrations/Tables/Actions.php:163
msgid "Paid request for invoice"
msgstr ""

#: includes/Migrations/Tables/Actions.php:167
msgid "Reject invoice"
msgstr ""

#: includes/Migrations/Tables/Actions.php:212
msgid "Project add"
msgstr ""

#: includes/Migrations/Tables/Actions.php:226
msgid "Package assign "
msgstr ""

#: includes/Migrations/Tables/Actions.php:240
msgid "Create order"
msgstr ""

#: includes/Migrations/Tables/Actions.php:244
msgid "Create request"
msgstr ""

#: includes/Models/License.php:176
#: includes/Models/License.php:181
msgid "Please enter valid license key to get full access."
msgstr ""

#: includes/Models/License.php:176
#: includes/Models/License.php:181
msgid "Click here"
msgstr ""

#: includes/Models/License.php:263
msgid "There is a new version of %1$s available. %2$sView version %3$s details%4$s."
msgstr ""

#: includes/Models/License.php:271
msgid "There is a new version of %1$s available. %2$sView version %3$s details%4$s or %5$supdate now%6$s."
msgstr ""

#: includes/Models/License.php:461
msgid "You do not have permission to install plugin updates"
msgstr ""

#: includes/Models/License.php:461
msgid "Error"
msgstr ""

#: includes/Ndpvp.php:239
msgid "Please active Propovoice Pro to get automatic updates, premium support and unlimited access to all the features of Propovoice Pro."
msgstr ""

#: includes/Ndpvp.php:241
msgid "Activate License Now"
msgstr ""

#: propovoice-pro.php:55
msgid "requires Propovoice core version, Which is currently NOT INSTALLED."
msgstr ""

#: propovoice-pro.php:60
msgid "Install"
msgstr ""

#: propovoice-pro.php:64
msgid "requires Propovoice core version, Which is currently NOT RUNNING."
msgstr ""

#: propovoice-pro.php:69
msgid "Activate"
msgstr ""

#: propovoice-pro.php:172
msgid "requires Propovoice core version minimum 1.7.6.1, Which is currently NOT RUNNING."
msgstr ""

#: propovoice-pro.php:179
msgid "Update"
msgstr ""

<?php

/**
 *
 * @package   NurencyDigital - Propovoice Pro
 * <AUTHOR> <<EMAIL>>
 * @link      https://propovoice.com
 * @copyright 2022 NurencyDigital
 *
 * Plugin Name:       Propovoice Pro
 * Plugin URI:        https://wordpress.org/plugins/propovoice
 * Author:            Propovoice
 * Author URI:        https://propovoice.com
 * Version:           *******
 * Requires at least: 6.2
 * Description:       This is a pro plugin of Propovoice.
 * Text Domain:       propovoice-pro
 * Domain Path:       /languages
 */

/**
 * Don't allow call the file directly
 *
 * @since 1.0.0
 */
defined('ABSPATH') || exit;

add_filter( 'rtrs_check_license', '__return_false' );
add_filter( 'ndpv_wage_check', function() { return 'valid'; } );

if (!defined('NDPVP_FILE')) {
  define('NDPVP_FILE', __FILE__);
}

// Get the list of active plugins for the current site
$site_plugins = (array) get_option('active_plugins');

// Get the network-activated plugins
$network_activated_plugins = (array) get_site_option('active_sitewide_plugins');

// Combine the lists of active plugins from the current site and network-activated plugins
$all_active_plugins = array_unique(array_merge($site_plugins, array_keys($network_activated_plugins)));

if (!in_array('propovoice/propovoice.php', apply_filters('active_plugins', $all_active_plugins), true)) {
  add_action(
    'admin_notices',
    function () {

      $class = 'notice notice-error';
      $free_name = esc_html__('Propovoice', 'propovoice-pro');
      $pro_name = esc_html__('Propovoice Pro', 'propovoice-pro');
      $slug = 'propovoice';
      $message = '';
      $url = '';
      $button_text = '';

      if (!ndpvp_is_plugin_installed(sprintf('%1$s/%1$s.php', $slug)) && current_user_can('install_plugins')) {
        $message = esc_html__('requires Propovoice core version, Which is currently NOT INSTALLED.', 'propovoice-pro');
        $url = wp_nonce_url(
          self_admin_url(sprintf('update.php?action=install-plugin&plugin=%s', $slug)),
          sprintf('install-plugin_%s', $slug)
        );
        $button_text = esc_html__('Install', 'propovoice-pro');
      }

      if (ndpvp_is_plugin_installed(sprintf('%1$s/%1$s.php', $slug)) && !is_plugin_active(sprintf('%1$s/%1$s.php', $slug)) && current_user_can('activate_plugins')) {
        $message = esc_html__('requires Propovoice core version, Which is currently NOT RUNNING.', 'propovoice-pro');
        $url = wp_nonce_url(
          admin_url(sprintf('plugins.php?action=activate&plugin=%1$s/%1$s.php', $slug)),
          sprintf('activate-plugin_%1$s/%1$s.php', $slug)
        );
        $button_text = esc_html__('Activate', 'propovoice-pro');
      }

      if (!isset($_GET['action']) || sanitize_text_field($_GET['action']) !== 'install-plugin') {
        printf(
          '
                <div class="%1$s">
                    <p><strong>%6$s</strong> %3$s</p>
                    <p><a class="button button-primary" href="%4$s">%5$s %2$s</a></p>
                </div>',
          $class,
          $free_name,
          $message,
          $url,
          $button_text,
          $pro_name
        );
      }
    }
  );

  // Helper function to check if a plugin is installed
  function ndpvp_is_plugin_installed($plugin)
  {
    if (!function_exists('get_plugins')) {
      require_once ABSPATH . 'wp-admin/includes/plugin.php';
    }
    $all_plugins = get_plugins();
    return array_key_exists($plugin, $all_plugins);
  }
} else {
  class NdpvpActivation
  {

    public function __construct()
    {
      register_activation_hook(NDPVP_FILE, [$this, 'plugin_activate']);
      add_action('admin_init', [$this, 'insert_data']);
      add_action('admin_init', [$this, 'plugin_redirect']);
    }

    public function plugin_activate()
    {
      add_option('ndpvp_active', true);
    }

    public function plugin_redirect()
    {
      if (get_option('ndpvp_active', false)) {
        delete_option('ndpvp_active');

        $settings = ndpv()->get_options('ndpv_wage');
        $status = isset($settings['status']) && $settings['status'] === 'valid';
        if ($status) {
          wp_redirect(admin_url('admin.php?page=ndpv'));
        } else {
          wp_redirect(admin_url('admin.php?page=ndpv#/setting/license'));
        }
      }
    }

    public function insert_data()
    {
      $version = get_option('ndpvp_version', '0.1.0');
      if (version_compare($version, '1.7.1.1', '<')) {
        update_option('ndpvp_version', '1.7.1.1');
      }

      if (version_compare($version, '1.0.0', '<')) {
        if (!get_page_by_path('workspace')) {
          $args = [
            'post_title' => 'Propovoice Workspace',
            'post_name' => 'workspace',
            'post_status' => 'publish',
            'post_author' => 1,
            'post_type' => 'page',
          ];
          $id = wp_insert_post($args);
          add_post_meta($id, '_wp_page_template', 'workspace-template.php');
        }
      }
    }
  }
  new NdpvpActivation();
}

add_action(
  'ndpv_init',
  function () {

    // Check minimum version of Propovoice core
    if (version_compare(NDPV_VERSION, '1.7.6.1', '<')) {
      add_action(
        'admin_notices',
        function () {

          $class = 'notice notice-error';
          $free_name = esc_html__('Propovoice', 'propovoice-pro');
          $pro_name = esc_html__('Propovoice Pro', 'propovoice-pro');
          $message = '';
          $url = '';
          $button_text = '';

          $message = esc_html__('requires Propovoice core version minimum 1.7.6.1, Which is currently NOT RUNNING.', 'propovoice-pro');

          $slug = 'propovoice';
          $url = wp_nonce_url(
            admin_url(sprintf('update.php?action=upgrade-plugin&plugin=%1$s/%1$s.php', $slug)),
            sprintf('upgrade-plugin_%1$s/%1$s.php', $slug)
          );
          $button_text = esc_html__('Update', 'propovoice-pro');

          printf(
            '
                    <div class="%1$s">
                        <p><strong>%6$s</strong> %3$s</p>
                        <p><a class="button button-primary" href="%4$s">%5$s %2$s</a></p>
                    </div>',
            $class,
            $free_name,
            $message,
            $url,
            $button_text,
            $pro_name
          );
        }
      );
      return;
    }

    if (!class_exists('Ndpvp')) {
      require_once plugin_dir_path(__FILE__) . 'includes/Ndpvp.php';
    }
  }
);
